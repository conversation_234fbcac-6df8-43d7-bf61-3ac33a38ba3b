# PeerConnect Admin

Guide for installing, building, and running the React (Vite) application for development, production, Docker, and Docker Compose environments, using Yarn or NPM.

## Prerequisites

- Node.js >= 18
- Yarn or NPM
- Docker (if running with container)
- Docker Compose (if using multi-container)

## Environment Variables

Create a `.env` file or provide environment variables when building/running:

```env
VITE_API_URL=http://staging.localhost:3000/api
```

## Install & Run Locally

### Yarn

```bash
yarn install
yarn dev
```

### NPM

```bash
npm install
npm run dev
```

- Access: http://localhost:3000

## Build for Production

### Yarn

```bash
yarn build
```

### NPM

```bash
npm run build
```

## Preview Production Build (Local)

```bash
yarn preview
# or
npm run preview
```

- Access: http://localhost:4173

## Lint & Format

```bash
yarn lint
yarn lint:fix
yarn prettier
yarn prettier:fix
# or use npm run ...
```

## Build & Run with Dockerfile

```bash
docker build \
  --build-arg VITE_API_URL=http://staging.localhost:3000/api \
  -t wellness-admin .

docker run -dp 4173:4173 -t wellness-admin
```

- Access: http://localhost:4173

## Build & Run with Docker Compose

```bash
VITE_API_URL=http://staging.localhost:3000/api docker-compose build --no-cache
docker-compose up -d --force-recreate

docker exec -it wellness-admin sh -c "cat /app/.env"
```

## Main Scripts (package.json)

- `dev`: Start dev server (Vite)
- `build`: Build for production
- `preview`: Preview production build
- `lint`, `lint:fix`: Lint/fix code
- `prettier`, `prettier:fix`: Check/format code
- `type-check`: TypeScript type check
