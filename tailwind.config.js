/** @type {import('tailwindcss').Config} */

module.exports = {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        layout: {
          background: '#FAFAFA'
        },
        text: {
          main: '#333333',
          link: '#1677FF',
          danger: '#EB5757',
          disabled: '#00000040',
          'info-primary': '#0288d1',
          'link-blue': '#1558D6',
          'sub-note': '#6F6F6F'
        },
        background: {
          main: '#B88770',
          'table-footer': '#00000005'
        },
        border: {
          description: '#0000000F'
        },
        theme: {
          link: '#06c',
          primary: '#b88770',
          'modal-gradient-top': '#F8F2F0',
          'modal-gradient-middle': '#F8F2F0',
          'modal-gradient-bottom': '#F8F3F1',
          'error-primary': '#ff4d4f'
        }
      }
    }
  },
  plugins: []
}
