import { Layout, Breadcrumb } from 'antd'
import { ItemType } from 'antd/es/breadcrumb/Breadcrumb'
import { Outlet } from 'react-router-dom'
import Sidebar from '@/components/Sidebar'
import Header from '@/components/Header'
import Loading from '@/components/Loading'
import { HomeOutlined } from '@ant-design/icons'
import { routePaths } from '@/constants/routesElement'
const { Content } = Layout

interface PrivateLayoutProps {
  children?: React.ReactNode
  breadcrumb?: {
    items: ItemType[]
    className?: string
  }
  loading?: boolean
  defaultOpenKeys?: string[]
  mainClassName?: string
}

const PrivateLayout = ({
  children,
  breadcrumb,
  loading = false,
  defaultOpenKeys,
  mainClassName
}: PrivateLayoutProps) => {
  return (
    <Layout className='min-h-screen overflow-hidden'>
      <Header />
      <Layout className='h-[calc(100vh-64px)] bg-layout-background' hasSider>
        <Sidebar defaultOpenKeys={defaultOpenKeys} />
        <Content className={`px-8 py-4 mb-4 rounded-lg overflow-auto ${mainClassName}`}>
          {breadcrumb && (
            <Breadcrumb
              items={[
                {
                  href: routePaths.dashboard,
                  title: <HomeOutlined />
                },
                ...breadcrumb.items
              ]}
              className={`${breadcrumb.className} mb-8`}
            />
          )}
          {children || <Outlet />}
        </Content>
        {loading && <Loading />}
      </Layout>
    </Layout>
  )
}

export default PrivateLayout
