import { SINGLE_DIGIT_REGEX } from '@/constants/regex'
import dayjs from 'dayjs'

interface FormatTimeProps {
  time?: string | Date
  format?: string
}

export const formatTime = ({
  time = new Date(),
  format = 'YYYY/MM/DD'
}: FormatTimeProps): string => {
  return dayjs(time).format(format)
}

export const getDate = (dateString = '') => {
  const to2digits = (num: number) => String(num).replace(SINGLE_DIGIT_REGEX, '0$1')

  const d = new Date(dateString)

  const month = (d.getMonth() + 1).toString()
  const date = d.getDate().toString()
  const year = d.getFullYear().toString()
  const dayOfWeek = d.getDay()
  const hours = d.getHours().toString()
  const minutes = to2digits(d.getMinutes())

  return {
    month,
    date,
    year,
    dayOfWeek,
    hours,
    minutes
  }
}

export const convertDateWithFormat = (
  dateString: string,
  type:
    | 'YearMonthDateWithSlash'
    | 'YearMonthDateWithString'
    | 'YearMonthDateWithoutMinute' = 'YearMonthDateWithSlash'
) => {
  const daysInWeek = ['日', '月', '火', '水', '木', '金', '土'] // Element 0 is Sunday

  const { date, month, year, dayOfWeek, hours, minutes } = getDate(dateString)

  switch (type) {
    case 'YearMonthDateWithSlash':
      return `${year}/${month}/${date}（${daysInWeek[dayOfWeek]}）${hours}:${minutes}`
    case 'YearMonthDateWithString':
      return `${year}年${month}月${date}日（${daysInWeek[dayOfWeek]}）`
    case 'YearMonthDateWithoutMinute':
      return `${year}/${month}/${date}（${daysInWeek[dayOfWeek]}）`
    default:
      return `${year}/${month}/${date}（${daysInWeek[dayOfWeek]}）${hours}:${minutes}`
  }
}
