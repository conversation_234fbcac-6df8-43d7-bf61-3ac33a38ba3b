export const objectToFormData = (
  obj: Record<string, unknown>,
  formData = new FormData(),
  namespace = ''
): FormData => {
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const formKey = namespace ? `${namespace}[${key}]` : key
      const value = obj[key]

      if (value === undefined || value === null) {
        continue
      } else if (value instanceof Date) {
        formData.append(formKey, value.toISOString())
      } else if (Array.isArray(value)) {
        // Handle arrays
        if (value.length > 0) {
          value.forEach((item, index) => {
            if (item === null || item === undefined) {
              return
            } else if (
              typeof item === 'object' &&
              !(item instanceof File) &&
              !(item instanceof Blob)
            ) {
              objectToFormData(item as Record<string, unknown>, formData, `${formKey}[${index}]`)
            } else {
              formData.append(`${formKey}[${index}]`, String(item))
            }
          })
        }
      } else if (
        typeof value === 'object' &&
        !(value instanceof File) &&
        !(value instanceof Blob)
      ) {
        objectToFormData(value as Record<string, unknown>, formData, formKey)
      } else {
        formData.append(formKey, String(value))
      }
    }
  }
  return formData
}

export const objectToQueryString = <T extends Record<string, unknown>>(params: T): string => {
  const searchParams = new URLSearchParams()

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString())
    }
  })

  const queryString = searchParams.toString()
  return queryString ? `?${queryString}` : ''
}
