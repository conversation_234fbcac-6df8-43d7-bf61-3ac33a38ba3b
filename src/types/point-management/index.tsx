export interface SuccessEntry {
  file_name: string
  status: string
  message: string
  log_id: string
  log_url: string
}

export interface FailureEntry {
  file_name: string
  message: string
  log_url: string
}

export type PointManagementResponse =
  | {
      success: SuccessEntry[]
      failure: []
    }
  | {
      success: []
      failure: FailureEntry[]
    }

export interface PointSubtractResponse {
  isSuccess: boolean
  data: {
    failedResults: []
  }
}
