import { UploadFile } from 'antd'
import dayjs from 'dayjs'

export interface IGetLotteryListParams {
  limit?: number
}

export interface ILotteryItem {
  lottery_id: string
  type: string
  pk: string
  unique_key: string
  lottery_name: string
  is_deleted: string
  preview_flag: string
  pause_flag: string
  summary: {
    top_text: string
    start_at: string
    ended_at: string
    campaign_company_id: string
    prize_img_link: string
    prize_name: string
    target_screenshot_explanation: string
    same_screenshot_img_link: string
    application_times: string
    notes: string
  }
  num_of_winners: number
  created_at: string
  updated_at: string
}

export type IGetLotteryListResponse = ILotteryItem[]

export interface ILotteryCreateResponse {
  is_deleted: string
  lottery_id: string
  lottery_name: string
  num_of_winners: number
  pause_flag: string
  pk: string
  preview_flag: string
  prizes: Prizes
  summary: Summary
  type: string
  unique_key: string
  updated_at: string
}

export interface Prizes {
  audience: Audience
  winners: Winner[]
}

export interface Audience {
  audience_id: string
  audience_name: string
}

export interface Winner {
  giftee_url: string
  peer_conne_id: string
  user_id: string
}

export interface Summary {
  application_times: string
  campaign_company_id: string
  ended_at: string
  prize_img_link: string
  prize_name: string
  same_screenshot_img_link: string
  start_at: string
  target_screenshot_explanation: string
}

export interface ILotteryFormValues {
  lottery_name: string
  prize_img?: UploadFile
  same_screenshot_img?: UploadFile
  same?: UploadFile
  preview_flag: boolean | string | number
  pause_flag: boolean | string | number
  num_of_winners: string | number
  campaign_company_id: string
  prize_name: string
  target_screenshot_explanation: string
  application_times: string
  notes?: string
  top_text?: string
  start_date: dayjs.Dayjs
  start_time: dayjs.Dayjs
  end_date: dayjs.Dayjs
  end_time: dayjs.Dayjs
}

export interface ILotteryDeleteResponse {
  success: boolean
}

export interface Root {
  is_deleted: string
  lottery_id: string
  lottery_name: string
  num_of_winners: number
  pause_flag: string
  pk: string
  preview_flag: string
  prizes: Prizes
  summary: Summary
  type: string
  unique_key: string
  updated_at: string
}
