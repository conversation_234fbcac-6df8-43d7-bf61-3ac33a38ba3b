export interface IGetPointHistoryParams {
  token?: string
  limit: number
}

export interface IUpdateReasonPointHistoryParams {
  reason?: string
}

export interface IUser {
  type: string
  peer_conne_id: string
  blob_url?: string
}

export interface IPointHistory {
  id: string
  type: string
  pk: string
  created_at: string
  admin_api_name: string
  endpoint: string
  is_succeed: string
  point_amount: number
  users: IUser
  company_id: string
  event_name: string
  reason?: string
}

export interface IGetPointHistoryResponse {
  data: IPointHistory[]
  hasMoreResults: boolean
  continuationToken: string
}

export interface ICsvRowError {
  [key: string]: {
    peer_conne_id: string
  }
}

export interface ICsvFileError {
  file_csv: ICsvRowError[]
}

export interface IApiErrorResponse {
  errors: ICsvFileError
}
