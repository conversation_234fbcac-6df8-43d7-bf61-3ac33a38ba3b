import type { UploadFile } from 'antd/es/upload/interface'
import dayjs from 'dayjs'

export interface IGetImageFormListParams {
  token?: string
  limit: number
}

export interface IGetImageFormParams {
  id?: string
}

export interface IGetImageFormListResponse {
  data: IImageFormList[]
  hasMoreResults: boolean
  continuationToken: string
}

export interface IImageFormList {
  unique_key: string
  type: string
  pk: string
  company_id: string
  image_count_max: number
  text: IImageFormText
  image: IImageFormImage
  start_at: string
  ended_at: string
  cycle: IImageFormCycle
  is_public: string
  created_at: string
  is_customer_form?: string
  updated_at?: string
}

export interface IImageFormResponse extends IImageFormList {
  is_customer_form?: string
  updated_at?: string
}

export interface IImageFormText {
  meta_title: string
  text_out_of_period: string
  text_description: string
  text_img_preview: string
  button_text_image_upload: string
  button_text_image_upload_another: string
  button_text_image_upload_max: string
  button_text_submit: string
  text_submitted: string
  reply_message: IImageFormReplyMessage
}

export interface IImageFormReplyMessage {
  type: string
  text: string
}

export interface IImageFormImage {
  top_image: string
  sample_image: string
}

export interface IImageFormCycle {
  is_monthly: '0' | '1'
  monthly_start_date: number
  monthly_end_date: number
}

export interface ICompanyData {
  company_id: string
  company_name: string
}

export interface IFormValues {
  company_id: string
  image_count_max: number
  meta_title: string
  start_date: dayjs.Dayjs
  start_time: dayjs.Dayjs
  end_date: dayjs.Dayjs
  end_time: dayjs.Dayjs
  cycle_start: number | null
  cycle_end: number | null
  is_public: string
  is_customer_form: string
  top_image: UploadFile
  sample_image: UploadFile
  text_description: string
  text_out_of_period: string
  text_img_preview: string
  button_text_image_upload_another: string
  button_text_image_upload_max: string
  button_text_image_upload: string
  button_text_submit: string
  text_submitted: string
  reply_message: string
}

export type TImageFormSortDate = 'ascend' | 'descend'
