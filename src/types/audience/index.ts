export interface IGetAudienceListParams {
  token?: string
  limit?: number
  id?: string
  name?: string
  from?: string
  to?: string
}

export interface AudienceInfo {
  id: string
  type: string
  pk: string
  pc_id_list_name: string
  pc_id_list_exists: string
  pc_id_list_blob_url?: string
  audience: {
    exists: string
    audience_id: number
    audience_created_at: string
  }
}

export interface IGetAudienceListResponse {
  data: AudienceInfo[]
  hasMoreResults: boolean
  continuationToken: string
}

export interface ICreateAudienceListNameResponse {
  id: string
  type: string
  pk: string
  pc_id_list_name: string
  pc_id_list_exists: string
  pc_id_list_blob_url?: string
  audience: AudienceExists
}

export interface AudienceExists {
  exists: string
}

export interface ICreateAndUpdateAudiencePayload {
  allow_override: string
}

export interface AudienceEditInfo {
  id: string
  audience_created_at: string
  pc_id_list_name: string
  pc_id_list_blob_url: string
}
