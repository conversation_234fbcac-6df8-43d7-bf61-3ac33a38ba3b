import { useState } from 'react'
import { notification, Upload } from 'antd'
import type { UploadFile, UploadProps } from 'antd/es/upload/interface'
import { RcFile } from 'antd/es/upload'
import { TEXT_ALERT_INVALID_FILE_EXTENSION, IMAGE_UPLOAD_VALIDATE } from '@/constants/img-form'
import { FILE_SIZE_LIMIT, INVALID_FILE_SIZE } from '@/constants/common'

export const useUploads = <T extends string>(keys: T[]) => {
  const [files, setFiles] = useState<Partial<Record<T, RcFile | null>>>({})
  const [fileLists, setFileLists] = useState<Partial<Record<T, UploadFile[]>>>({})

  const beforeUpload = (file: RcFile) => {
    const extension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase()
    const isValidExtension = IMAGE_UPLOAD_VALIDATE.includes(extension)

    if (!isValidExtension) {
      notification.error({
        message: TEXT_ALERT_INVALID_FILE_EXTENSION,
        placement: 'topRight'
      })
      return Upload.LIST_IGNORE
    }

    if (file.size > FILE_SIZE_LIMIT) {
      notification.error({
        message: INVALID_FILE_SIZE,
        placement: 'topRight'
      })
      return Upload.LIST_IGNORE
    }

    return false
  }

  const result: Partial<Record<string, unknown>> = { beforeUpload }

  keys.forEach((key) => {
    result[`${key}File`] = files[key] ?? null
    result[`${key}FileList`] = fileLists[key] ?? []
    result[`${key}OnChange`] = ({ fileList }: { fileList: UploadFile[] }) => {
      setFileLists((prev) => ({ ...prev, [key]: fileList }))
      setFiles((prev) => ({ ...prev, [key]: fileList[0]?.originFileObj ?? null }))
    }
  })

  return result as {
    beforeUpload: typeof beforeUpload
  } & {
    [K in T as `${K}File`]: RcFile | null
  } & {
    [K in T as `${K}FileList`]: UploadFile[]
  } & {
    [K in T as `${K}OnChange`]: UploadProps['onChange']
  }
}
