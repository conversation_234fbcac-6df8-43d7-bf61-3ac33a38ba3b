import { App } from 'antd'

type NotificationType = 'success' | 'error' | 'info' | 'warning'
type NotificationPlacement = 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight' | 'top'

interface NotificationOptions {
  type?: NotificationType
  message?: string
  description?: string
  placement?: NotificationPlacement
  className?: string
  duration?: number
}
export const useNotification = () => {
  const { notification } = App.useApp()

  const showNotification = ({
    type = 'success',
    message,
    description,
    placement = 'top',
    className,
    duration = 6
  }: NotificationOptions) => {
    const position = type === 'error' && !placement ? 'topRight' : placement || 'top'

    notification[type]({
      message,
      description,
      placement: position,
      className,
      duration
    })
  }

  return {
    showNotification
  }
}
