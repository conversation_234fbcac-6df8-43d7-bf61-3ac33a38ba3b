import { useState } from 'react'
import { useFetch } from './useFetch'
import companyApi from '@/services/internal/modules/company'
import { IGetCompaniesResponse } from '@/types/company'
import { ICompanyData } from '@/types/image-form'

export const useCompaniesList = () => {
  const [companiesList, setCompaniesList] = useState<ICompanyData[]>([])

  const sortCompanies = (companies: ICompanyData[]) => {
    const getBaseId = (str: string) => str.match(/^(C\d+)/)?.[1] || str
    const getAlias = (str: string) => str.split('_')[1] || ''

    return companies.sort((a, b) => {
      const baseA = getBaseId(a.company_id)
      const baseB = getBaseId(b.company_id)
      const aliasA = getAlias(a.company_id)
      const aliasB = getAlias(b.company_id)

      if (baseA === baseB) {
        if (aliasA !== aliasB) {
          return aliasA.localeCompare(aliasB)
        }
        return a.company_id.length - b.company_id.length
      }

      const numA = parseInt(baseA.substring(1))
      const numB = parseInt(baseB.substring(1))
      return numA - numB
    })
  }

  const { isFetching } = useFetch<IGetCompaniesResponse>({
    fetcher: companyApi.getCompanies,
    handleResponse: (res: IGetCompaniesResponse) => {
      if (res) {
        const transformedData = res.map((item) => ({
          company_id: item.company_id,
          company_name: item.company_name
        }))
        setCompaniesList(sortCompanies(transformedData))
      }
      return res
    },
    dependencies: []
  })

  return {
    companiesList,
    isFetching
  }
}
