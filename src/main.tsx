import ReactDOM from 'react-dom/client'
import './style/scss/main.scss'
import { ConfigProvider, App as AntApp } from 'antd'
import { theme } from './constants/common'
import MainApp from '@/App'

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <ConfigProvider theme={theme}>
    <AntApp
      notification={{
        placement: 'top',
        top: 10,
        getContainer: () => document.getElementById('custom-create-success-toast') || document.body
      }}
    >
      <MainApp />
      <div id='custom-create-success-toast' className='custom-create-success-toast'></div>
    </AntApp>
  </ConfigProvider>
)
