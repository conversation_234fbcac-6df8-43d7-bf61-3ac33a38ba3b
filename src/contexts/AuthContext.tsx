/* eslint-disable react-refresh/only-export-components */
/* eslint-disable no-unused-vars */
import React, { createContext, useContext, useState, useEffect } from 'react'
import { authService } from '@/utils/auth'
import authApi from '@/services/internal/modules/auth'

interface AuthContextType {
  isAuthenticated: boolean
  isLoading: boolean
  handleLogin: (token: string, userName: string) => void
  handleLogout: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkAuthStatus = () => {
      const isAuth = authService.isAuthenticated()
      setIsAuthenticated(isAuth)
      setIsLoading(false)
    }
    checkAuthStatus()
  }, [])

  const handleLogin = (token: string, userName: string) => {
    authService.setToken(token)
    authService.setUserName(userName)
    setIsAuthenticated(true)
  }

  const handleLogout = async () => {
    try {
      setIsLoading(true)
      await authApi.logout()

      authService.removeToken()
      authService.removeUserName()
      setIsAuthenticated(false)
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return null // Or a loading spinner
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, isLoading, handleLogin, handleLogout }}>
      {children}
    </AuthContext.Provider>
  )
}
