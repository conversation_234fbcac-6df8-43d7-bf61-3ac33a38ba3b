import { BrowserRouter as Router, Routes, Route, Navigate, Outlet } from 'react-router-dom'
import { privateRoutes, publicRoutes, routePaths } from '@/constants/routesElement'
import { AuthProvider } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/ProtectedRoute'
import PublicRoute from '@/components/PublicRoute'
import { useNetwork } from './hooks/use-network'
import LostConnectionModal from './components/LostConnectionModal'

const App = () => {
  const { online } = useNetwork()

  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public Routes */}
          {publicRoutes.map((route) => (
            <Route
              key={route.key}
              path={route.path}
              element={<PublicRoute>{route.element}</PublicRoute>}
            />
          ))}

          {/* Private Routes */}
          <Route
            path='/'
            element={
              <ProtectedRoute>
                <Outlet />
              </ProtectedRoute>
            }
          >
            <Route index element={<Navigate to={routePaths.dashboard} replace />} />
            {privateRoutes.map((route) => (
              <Route key={route.key} path={route.path.replace('/', '')} element={route.element} />
            ))}
          </Route>
        </Routes>
      </Router>
      <LostConnectionModal isOpen={!online} />
    </AuthProvider>
  )
}

export default App
