import { ReactNode } from 'react'
import { HomeOutlined, SolutionOutlined } from '@ant-design/icons'
import Dashboard from '@/pages/private/Dashboard'
import Login from '@/pages/public/Login'
import FunctionOutlined from '@/assets/icons/FunctionOutlined.svg'
import {
  ImageFormList,
  ChangePassword,
  ImageFormEdit,
  ImageFormCreate,
  AudienceList,
  UserPointList,
  LotteryList,
  LotteryCreate,
  LotteryEdit
} from '@/pages'

export interface RouteConfig {
  key: string
  path: string
  label?: string
  icon?: ReactNode
  element?: ReactNode
  children?: RouteConfig[]
}
export const routePaths = {
  // Public routes
  login: '/login',
  register: '/register',

  // Private routes
  dashboard: '/dashboard',
  changePassword: '/change-password',
  imgForm: {
    list: '/img-form',
    edit: '/img-form/edit/:id',
    create: '/img-form/create'
  },
  audience: {
    list: '/audience'
  },
  userPoint: {
    list: '/user-points'
  },
  lottery: {
    list: '/lottery',
    create: '/lottery/create',
    edit: '/lottery/edit/:id'
  }
} as const

export const ROUTE_LABELS = {
  LOGIN: 'Login',
  DASHBOARD: 'アカウント管理',
  LOGOUT: 'ログアウト',
  CHANGE_PASSWORD: 'パスワード変更',
  IMG_FORM: '画像応募フォーム一覧',
  AUDIENCE: 'ユーザーリストオーディエンス',
  LOTTERY: '抽選一覧'
} as const

export const ROUTE_KEYS = {
  LOGIN: 'login',
  DASHBOARD: 'dashboard',
  LOGOUT: 'logout',
  CHANGE_PASSWORD: 'change-password',
  IMG_FORM: 'img-form',
  AUDIENCE: 'audience',
  USER_POINT: 'user-point',
  LOTTERY: 'lottery'
} as const

export type RouteKey = keyof typeof ROUTE_LABELS
export type RouteKeyValue = (typeof ROUTE_KEYS)[RouteKey]

// Public Routes with Elements
export const publicRoutes: RouteConfig[] = [
  {
    key: ROUTE_KEYS.LOGIN,
    path: routePaths.login,
    label: ROUTE_LABELS.LOGIN,
    element: <Login />
  }
]

// Private Routes with Elements
export const privateRoutes: RouteConfig[] = [
  {
    key: ROUTE_KEYS.DASHBOARD,
    path: routePaths.dashboard,
    element: <Dashboard />
  },
  {
    key: ROUTE_KEYS.CHANGE_PASSWORD,
    path: routePaths.changePassword,
    element: <ChangePassword />
  },
  // Image Form Routes
  {
    key: ROUTE_KEYS.IMG_FORM,
    path: routePaths.imgForm.list,
    element: <ImageFormList />
  },
  {
    key: ROUTE_KEYS.IMG_FORM,
    path: routePaths.imgForm.edit,
    element: <ImageFormEdit />
  },
  {
    key: ROUTE_KEYS.IMG_FORM,
    path: routePaths.imgForm.create,
    element: <ImageFormCreate />
  },
  // Audience Routes
  {
    key: ROUTE_KEYS.AUDIENCE,
    path: routePaths.audience.list,
    element: <AudienceList />
  },
  // point history Routes
  {
    key: ROUTE_KEYS.USER_POINT,
    path: routePaths.userPoint.list,
    element: <UserPointList />
  },
  // lottery Routes
  {
    key: ROUTE_KEYS.LOTTERY,
    path: routePaths.lottery.list,
    label: ROUTE_LABELS.LOTTERY,
    element: <LotteryList />
  },
  {
    key: ROUTE_KEYS.LOTTERY,
    path: routePaths.lottery.create,
    label: ROUTE_LABELS.LOTTERY,
    element: <LotteryCreate />
  },
  {
    key: ROUTE_KEYS.LOTTERY,
    path: routePaths.lottery.edit,
    label: ROUTE_LABELS.LOTTERY,
    element: <LotteryEdit />
  }
]

export const CATEGORY_MENUS_ITEMS = [
  {
    key: '/',
    label: '管理者サイトTOP'
  },
  {
    key: 'sub2',
    label: 'ユーザー管理'
  },
  {
    key: 'sub3',
    label: '企業管理'
  },
  {
    key: 'sub4',
    label: '企業別機能管理'
  },
  {
    key: 'sub5',
    label: 'ピアコネ機能管理'
  },
  {
    key: 'sub6',
    label: '外部サービス管理'
  },
  {
    key: 'sub7',
    label: 'リリース後コマンド'
  }
] as const

// Sidebar Menu Items
export const sidebarMenuItems = [
  {
    key: CATEGORY_MENUS_ITEMS[0].key,
    icon: <HomeOutlined />,
    label: CATEGORY_MENUS_ITEMS[0].label
  },
  {
    key: CATEGORY_MENUS_ITEMS[1].key,
    icon: <SolutionOutlined />,
    label: CATEGORY_MENUS_ITEMS[1].label,
    children: [
      { label: 'ポイント操作一覧', key: routePaths.userPoint.list },
      { label: ROUTE_LABELS.AUDIENCE, key: routePaths.audience.list }
    ]
  },
  // {
  //   key: CATEGORY_MENUS_ITEMS[2].key,
  //   icon: <img src={CompanyOutlined} alt='Company Icon' style={{ width: '1em', height: '1em' }} />,
  //   label: CATEGORY_MENUS_ITEMS[2].label,
  //   children: []
  // },
  {
    key: CATEGORY_MENUS_ITEMS[3].key,
    icon: (
      <img src={FunctionOutlined} alt='Function Icon' style={{ width: '1em', height: '1em' }} />
    ),
    label: CATEGORY_MENUS_ITEMS[3].label,
    children: [
      { label: ROUTE_LABELS.IMG_FORM, key: routePaths.imgForm.list },
      { label: ROUTE_LABELS.LOTTERY, key: routePaths.lottery.list }
    ]
  }
  // {
  //   key: CATEGORY_MENUS_ITEMS[4].key,
  //   icon: <img src={ConnectOutlined} alt='Connect Icon' style={{ width: '1em', height: '1em' }} />,
  //   label: CATEGORY_MENUS_ITEMS[4].label,
  //   children: []
  // },
  // {
  //   key: CATEGORY_MENUS_ITEMS[5].key,
  //   icon: (
  //     <img src={HandshakeOutlined} alt='Handshake Icon' style={{ width: '1em', height: '1em' }} />
  //   ),
  //   label: CATEGORY_MENUS_ITEMS[5].label,
  //   children: []
  // },
  // {
  //   key: CATEGORY_MENUS_ITEMS[6].key,
  //   icon: <UploadOutlined />,
  //   label: CATEGORY_MENUS_ITEMS[6].label,
  //   children: []
  // }
] as const
