import { ThemeConfig } from 'antd'

export const theme: ThemeConfig = {
  token: {
    colorText: '#333333'
  },
  components: {
    Skeleton: {
      gradientFromColor: '#FAFAFA'
    }
  }
}

export const UNKNOWN = 'unknown'

export const ACCESS_TOKEN = 'access_token'
export const USER_NAME = 'user_name'
export const EMPTY_MARK = '-'

//FILE
export const FILE_SIZE_LIMIT = 5 * 1024 * 1024

//MESSAGE
export const INVALID_FILE_FORMAT_CSV = 'CSV形式のファイルをアップロードしてください。'
export const INVALID_FILE_SIZE =
  '画像のサイズが5MBを超えています。画像1個のサイズを5MB以下にしてアップロードしてください。'
