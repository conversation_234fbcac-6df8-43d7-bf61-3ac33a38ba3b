import axios, { AxiosError, AxiosInstance, AxiosResponse } from 'axios'
import {
  CREATED,
  INTERNAL_SERVER_ERROR,
  SUCCEEDED,
  UNAUTHENTICATED
} from '@/constants/statusResponse'
import { cookiesHelper } from '@/utils/cookies'
import { ACCESS_TOKEN } from '@/constants/common'
import { notification } from 'antd'
import { parseErrorDetails } from '@/utils/errorHandling'
import { messages } from '@/constants/message'
let hasAuthError = false
const LOGIN_API_URL = '/login'

export const customAxiosInstance = (baseURL: string): AxiosInstance => {
  const axiosInstance: AxiosInstance = axios.create({
    baseURL,
    withCredentials: false,
    timeout: 1000 * 40,
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Allow-Method': 'GET, POST, PATCH, PUT, DELETE, OPTIONS'
    }
  })

  return axiosInstance
}

export const customAxiosInterceptors = (axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.request.use(
    (config) => {
      if (hasAuthError) {
        // block request api if have auth error before
        return Promise.reject(new axios.Cancel('Request cancelled due to previous 401'))
      }
      const accessToken = cookiesHelper.get(ACCESS_TOKEN) || ''
      if (accessToken) {
        config.headers.Authorization = `Bearer ${accessToken}`
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  ),
    axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        if (response.status === SUCCEEDED || response.status === CREATED) {
          return response.data
        }
        return response
      },
      (error: AxiosError) => {
        const { response, config } = error
        const { isTimeout } = parseErrorDetails(error)

        if (response) {
          if (response.status === UNAUTHENTICATED && config?.url !== LOGIN_API_URL) {
            if (!hasAuthError) {
              notification.error({
                message: 'Unauthorized',
                placement: 'topRight'
              })
            }
            hasAuthError = true
            cookiesHelper.clearAll()
            window.location.href = '/login'
          } else if (response.status === INTERNAL_SERVER_ERROR) {
            console.log('internal server error')
          } else {
            console.log('unexpected error')
          }
        }

        if (isTimeout) {
          notification.error({
            message: messages.error.timeoutError,
            placement: 'topRight'
          })
        }

        return Promise.reject(error)
      }
    )
}

export const customAxiosService = (axiosInstance: AxiosInstance) => {
  const service = {
    get<T = unknown>(url: string, data?: object): Promise<T> {
      return axiosInstance.get(url, { params: data })
    },

    post<T = unknown>(url: string, data?: object, config?: object): Promise<T> {
      return axiosInstance.post(url, data, config)
    },

    put<T = unknown>(url: string, data?: object, config?: object): Promise<T> {
      return axiosInstance.put(url, data, config)
    },

    delete<T = unknown>(url: string, data?: object): Promise<T> {
      return axiosInstance.delete(url, data)
    },

    upload<T = unknown>(url: string, file: FormData | File): Promise<T> {
      return axiosInstance.post(url, file, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
    },

    patch<T = unknown>(url: string, file: FormData | File): Promise<T> {
      return axiosInstance.patch(url, file, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
    }
  }

  return service
}
