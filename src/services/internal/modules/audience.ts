import {
  ICreateAndUpdateAudiencePayload,
  ICreateAudienceListNameResponse,
  IGetAudienceListParams,
  IGetAudienceListResponse
} from '@/types/audience'
import { objectToQueryString } from '@/utils/convert'
import service from '..'

const basePath = '/pc-ids'

const audienceApi = {
  getAudienceList: (params: Partial<IGetAudienceListParams> = {}) =>
    service.get<IGetAudienceListResponse>(`${basePath}${objectToQueryString(params)}`),
  createAudienceListName: (name: string) =>
    service.post<ICreateAudienceListNameResponse>(`${basePath}`, { pc_id_list_name: name }),
  importAudienceList: (id: string, formData: FormData) =>
    service.upload<ICreateAudienceListNameResponse>(`${basePath}/${id}/imports`, formData),
  createAndUpdateAudience: (id: string, payload: ICreateAndUpdateAudiencePayload) =>
    service.put<ICreateAudienceListNameResponse>(`${basePath}/${id}/set-audience`, payload)
}

export default audienceApi
