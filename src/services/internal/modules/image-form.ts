import {
  IGetImageFormListParams,
  IGetImageFormParams,
  IGetImageFormListResponse,
  IImageFormList,
  IImageFormResponse
} from '@/types/image-form'
import service from '..'

const basePath = '/image-form'

const imageFormApi = {
  getImageFormList: (params: IGetImageFormListParams) =>
    service.get<IGetImageFormListResponse>(
      `${basePath}?token=${params.token || ''}&limit=${params.limit}`
    ),
  createImageForm: (formData: FormData) => {
    return service.upload<IImageFormList>(basePath, formData)
  },
  editImageForm: (formData: FormData, id: string) => {
    return service.patch<IImageFormList>(`${basePath}/${id}`, formData)
  },
  getImageForm: (params: IGetImageFormParams) =>
    service.get<IImageFormResponse>(`${basePath}/${params.id}`)
}

export default imageFormApi
