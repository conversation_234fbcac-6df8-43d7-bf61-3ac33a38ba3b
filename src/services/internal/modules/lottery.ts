import {
  IGetLotteryListParams,
  IGetLotteryListResponse,
  ILotteryCreateResponse,
  ILotteryDeleteResponse,
  ILotteryItem
} from '@/types/lottery'
import { objectToQueryString } from '@/utils/convert'
import service from '..'

const lotteryApi = {
  getLotteryList: (companyId: string, params: Partial<IGetLotteryListParams> = {}) =>
    service.get<IGetLotteryListResponse>(
      `companies/${companyId}/lotteries${objectToQueryString(params)}`
    ),
  getLotteryDetail: (lotteryId: string) => service.get<ILotteryItem>(`/lotteries/${lotteryId}`),
  createLottery: (formData: FormData) =>
    service.upload<ILotteryCreateResponse>('/lotteries', formData),
  deleteLottery: (lotteryId: string) =>
    service.delete<ILotteryDeleteResponse>(`/lotteries/${lotteryId}`),
  updateLottery: (formData: FormData, lotteryId: string) =>
    service.patch<ILotteryCreateResponse>(`/lotteries/${lotteryId}`, formData),
  updatePreviewFlag: (payload: { status: string }, lotteryId: string) =>
    service.put<ILotteryDeleteResponse>(
      `/lotteries/change-status-preview-flag/${lotteryId}`,
      payload
    ),
  updatePauseFlag: (payload: { status: string }, lotteryId: string) =>
    service.put<ILotteryDeleteResponse>(
      `/lotteries/change-status-pause-flag/${lotteryId}`,
      payload
    ),
  duplicateLottery: (payload: IDuplicateLotteryPayload, lotteryId: string) =>
    service.post<ILotteryItem>(`/lotteries/duplicate/${lotteryId}`, payload)
}

export default lotteryApi
