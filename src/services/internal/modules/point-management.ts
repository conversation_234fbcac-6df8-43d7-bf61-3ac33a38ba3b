/* eslint-disable @typescript-eslint/no-explicit-any */
import { PointManagementResponse, PointSubtractResponse } from '@/types/point-management'
import service from '..'

const basePath = '/point'

const pointManagementApi = {
  addPoint: (formData: FormData) => {
    return service.upload<PointManagementResponse>(`${basePath}/users/adds`, formData)
  },
  subtractPoint: (formData: FormData) => {
    return service.upload<PointSubtractResponse>(`${basePath}/users/subtract`, formData)
  }
}

export default pointManagementApi
