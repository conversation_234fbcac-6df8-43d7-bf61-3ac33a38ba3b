import service from '..'
import {
  IGetPointHistoryParams,
  IGetPointHistoryResponse,
  IPointHistory,
  IUpdateReasonPointHistoryParams
} from '@/types/point-history'

const basePath = '/admin-point-history'

const pointHistoryApi = {
  getPointHistoryList: (params: IGetPointHistoryParams) =>
    service.get<IGetPointHistoryResponse>(
      `${basePath}?token=${params.token || ''}&limit=${params.limit}`
    ),

  updateReasonPointHistory: (id: string, params: IUpdateReasonPointHistoryParams) =>
    service.put<IPointHistory>(`${basePath}/${id}`, params)
}

export default pointHistoryApi
