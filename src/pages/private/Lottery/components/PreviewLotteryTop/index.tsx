import { Button } from 'antd'
import { ONCE_A_DAY } from '../../contstant'
import { RcFile } from 'antd/lib/upload'
import { convertDateWithFormat } from '@/utils/dateTime'

interface PreviewLotteryTopProps {
  prizeImg: RcFile | null
  topText: string
  numOfWinners: number | string
  prizeName: string
  startLottery: string
  endLottery: string
  applicationTimes: string
  targetScreenshotExplanation: string
  sameScreenshotLink: RcFile | null
}

const PreviewLotteryTop: React.FC<PreviewLotteryTopProps> = ({
  prizeImg,
  topText,
  numOfWinners,
  prizeName,
  startLottery,
  endLottery,
  applicationTimes,
  targetScreenshotExplanation,
  sameScreenshotLink
}) => {
  const formattedStartLottery = startLottery
    ? convertDateWithFormat(startLottery, 'YearMonthDateWithoutMinute')
    : ''
  const formattedEndLottery = endLottery
    ? convertDateWithFormat(endLottery, 'YearMonthDateWithoutMinute')
    : ''

  return (
    <div className='w-full flex flex-col justify-between'>
      <div className='text-sm font-normal space-y-6'>
        {prizeImg ? (
          <div className='w-full'>
            <img
              className='rounded'
              alt='Lottery'
              src={
                typeof prizeImg === 'string'
                  ? prizeImg
                  : prizeImg instanceof File
                    ? URL.createObjectURL(prizeImg)
                    : ''
              }
            />
          </div>
        ) : (
          <div className='mx-auto w-full h-[160px] rounded bg-[#e6e6e6]'></div>
        )}
        <div className='space-y-4'>
          <div className='space-y-2'>
            {topText && (
              <p
                dangerouslySetInnerHTML={{
                  __html: topText
                }}
              />
            )}
            <p>
              対象となる画像の登録1枚につき、１口応募できます。応募者の中から抽選で
              <strong>{numOfWinners}</strong>名様に
              <strong>{prizeName}</strong>をプレゼントします。
            </p>
            <p>
              <span className='font-medium'>詳細は</span>
              <span className='text-theme-link cursor-pointer underline decoration-solid'>
                こちら
              </span>
              から
            </p>
          </div>
          <div>
            <h3 className='font-bold mb-1'>応募期間</h3>
            <p className='mb-3'>
              {formattedStartLottery} ～ {formattedEndLottery}
            </p>
            {applicationTimes === ONCE_A_DAY ? (
              <ul>
                <li>※ 1日1回を上限に、期間中は毎日応募可能です。毎日の応募で当選確率もUP！</li>
                <li>
                  ※ 毎日ご応募いただいた場合でも、当キャンペーンのご当選はおひとり様1回となります。
                </li>
              </ul>
            ) : (
              <ul>
                <li>※ 期間中、ご応募はお一人様1回限りとさせていただきます。</li>
              </ul>
            )}
          </div>
          <div>
            <h3 className='font-bold mb-1'>応募資格</h3>
            <ul>
              <li>・ピアコネLINE公式アカウント会員</li>
            </ul>
          </div>
          <div>
            <h3 className='font-bold mb-1'>対象となる画像</h3>
            <p
              dangerouslySetInnerHTML={{
                __html: targetScreenshotExplanation
              }}
            />
          </div>
          {sameScreenshotLink ? (
            <div className='mx-auto h-[208px]'>
              <img
                className='h-full w-auto max-w-full mx-auto object-cover'
                alt='Same screenshot image'
                src={
                  typeof sameScreenshotLink === 'string'
                    ? sameScreenshotLink
                    : sameScreenshotLink instanceof File
                      ? URL.createObjectURL(sameScreenshotLink)
                      : ''
                }
              />
            </div>
          ) : (
            <div className='mx-auto w-[117px] h-[208px] rounded bg-[#e6e6e6]'></div>
          )}
        </div>
      </div>
      <div className='pt-8'>
        <Button
          type='primary'
          className='w-full h-10 text-sm rounded shrink-0 btn-external-primary font-bold'
        >
          画像をアップロードする
        </Button>
      </div>
    </div>
  )
}

export default PreviewLotteryTop
