import { useState } from 'react'
import { notification } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { LOTTERY_TOP } from '../contstant.ts'
import { ILotteryFormValues } from '@/types/lottery/index.ts'

export const useLotteryPreview = (form: FormInstance<ILotteryFormValues>) => {
  const [previewFormValues, setPreviewFormValues] = useState<ILotteryFormValues | null>(null)
  const [previewScreen, setPreviewScreen] = useState(LOTTERY_TOP)

  const handlePreviewForm = () => {
    try {
      const values = form.getFieldsValue()
      setPreviewFormValues(values)
    } catch (error) {
      notification.error({
        message: 'プレビューの更新中にエラーが発生しました',
        placement: 'topRight'
      })
    }
  }

  return {
    previewFormValues,
    previewScreen,
    setPreviewScreen,
    handlePreviewForm
  }
}
