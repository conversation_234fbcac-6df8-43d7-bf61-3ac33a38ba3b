import { useState } from 'react'
import dayjs from 'dayjs'
import lotteryApi from '@/services/internal/modules/lottery'
import { ILotteryFormValues } from '@/types/lottery'
import { RcFile } from 'antd/lib/upload'
import { useNavigate } from 'react-router-dom'
import { objectToFormData } from '@/utils/convert'
import { routePaths } from '@/constants/routesElement'
import { parseApiErrorMessages, parseErrorDetails } from '@/utils/errorHandling'
import { useNotification } from '@/hooks/useNotification'

export const useLotteryFormSubmit = (
  prizeImgFile: RcFile | null,
  screenshotImgFile: RcFile | null,
  id?: string
) => {
  const [isLoadingSubmitForm, setIsLoadingSubmitForm] = useState(false)
  const [isSubmitError, setIsSubmitError] = useState(false)
  const [isSubmitSuccess, setIsSubmitSuccess] = useState(false)
  const [errorMessage, setErrorMessage] = useState<{ title: string; message: string }>({
    title: '',
    message: ''
  })
  const navigate = useNavigate()
  const { showNotification } = useNotification()

  const handleSubmit = async (values: ILotteryFormValues) => {
    setIsLoadingSubmitForm(true)
    setIsSubmitError(false)
    setErrorMessage({ title: '', message: '' })

    try {
      const formatDateTime = (date: dayjs.Dayjs, time: dayjs.Dayjs) =>
        dayjs(date).hour(time.hour()).minute(time.minute()).second(0).format('YYYY-MM-DDTHH:mm:ss')

      const startDateTime = formatDateTime(values.start_date, values.start_time)
      const endDateTime = formatDateTime(values.end_date, values.end_time)

      const formDataObj = {
        lottery_name: values.lottery_name,
        preview_flag: values.preview_flag ? 1 : 0,
        pause_flag: values.pause_flag ? 1 : 0,
        num_of_winners: values.num_of_winners,
        summary: {
          start_at: startDateTime,
          ended_at: endDateTime,
          campaign_company_id: values.campaign_company_id,
          prize_name: values.prize_name,
          target_screenshot_explanation: values.target_screenshot_explanation,
          application_times: values.application_times,
          notes: values.notes || '',
          top_text: values.top_text || ''
        }
      }

      const formData = objectToFormData(formDataObj)
      if (prizeImgFile) formData.append('prize_img', prizeImgFile)
      if (screenshotImgFile) formData.append('same_screenshot_img', screenshotImgFile)

      if (id) {
        await lotteryApi.updateLottery(formData, id)
        await lotteryApi.updatePreviewFlag({ status: values.preview_flag ? '1' : '0' }, id)
        await lotteryApi.updatePauseFlag({ status: values.pause_flag ? '1' : '0' }, id)

        navigate(routePaths.lottery.list)
        showNotification({ message: `${values.lottery_name}を更新しました。` })
      } else {
        const response = await lotteryApi.createLottery(formData)
        if (response) {
          navigate(routePaths.lottery.list)
          showNotification({ message: `${response.lottery_name}を作成しました。` })
        }
      }
      setIsSubmitSuccess(true)
    } catch (err: unknown) {
      setIsSubmitError(true)
      if (id) {
        const { errorMessage } = parseErrorDetails(err)
        errorMessage && setErrorMessage({ title: '更新エラー', message: errorMessage })
      }
      const errorDetails = parseApiErrorMessages(err)
      errorDetails && setErrorMessage(errorDetails)
    } finally {
      setIsLoadingSubmitForm(false)
    }
  }

  const handleCloseErrorModal = () => setIsSubmitError(false)

  return {
    isSubmitSuccess,
    isLoadingSubmitForm,
    isSubmitError,
    errorMessage,
    handleSubmit,
    handleCloseErrorModal
  }
}
