import PrivateLayout from '@/layouts/PrivateLayout'
import { CATEGORY_MENUS_ITEMS, routePaths } from '@/constants/routesElement'
import { Form, Input, DatePicker, TimePicker, Radio, Button, Modal, Switch } from 'antd'
import { CloseOutlined, WarningFilled } from '@ant-design/icons'
import classNames from 'classnames'
import { useUploads } from '@/hooks/useUploads'
import Select from '@/components/Select'
import SingleUpload from '@/components/SingleUpload'
import { useCompaniesList } from '@/hooks/useCompaniesList'
import { LABELS, LOTTERY_TOP, PREVIEW_OPTIONS } from '../contstant.ts'
import { useLotteryFormSubmit } from '../hooks/useLotteryFormSubmit'
import PreviewLotteryTop from '../components/PreviewLotteryTop/index.tsx'
import PreviewLotteryAbout from '../components/PreviewLotteryAbout/index.tsx'
import { useLotteryPreview } from '../hooks/useLotteryPreview.ts'
import { ILotteryFormValues, ILotteryItem } from '@/types/lottery/index.ts'
import { useEffect, useState } from 'react'
import { fieldRequiredMessage } from '@/utils/form.tsx'
import { useNavigate, useParams } from 'react-router-dom'
import lotteryApi from '@/services/internal/modules/lottery.ts'
import { useFetch } from '@/hooks/useFetch.ts'
import dayjs from 'dayjs'

const { TextArea } = Input

const LotteryDuplicate = () => {
  const [lotteryDetail, setLotteryDetail] = useState<ILotteryItem>()
  const [previewFlag, setPreviewFlag] = useState(false)
  const [pauseFlag, setPauseFlag] = useState(false)

  const navigate = useNavigate()
  const { id } = useParams()
  const [form] = Form.useForm<ILotteryFormValues>()

  const {
    prizeImageFile,
    prizeImageFileList,
    sameScreenShotImageFile,
    sameScreenShotImageFileList,
    prizeImageOnChange,
    sameScreenShotImageOnChange,
    beforeUpload
  } = useUploads(['prizeImage', 'sameScreenShotImage'])

  const { previewFormValues, previewScreen, setPreviewScreen, handlePreviewForm } =
    useLotteryPreview(form)
  // Note: We don't pass id to useLotteryFormSubmit since this is a new lottery creation
  const { isLoadingSubmitForm, handleSubmit, isSubmitError, errorMessage, handleCloseErrorModal } =
    useLotteryFormSubmit(prizeImageFile, sameScreenShotImageFile)
  const { companiesList, isFetching: isFetchingCompanies } = useCompaniesList()

  // Use duplicateLottery API instead of getLotteryDetail
  const { isFetching: isFetchingLotteryDetail } = useFetch<ILotteryItem>({
    fetcher: () => lotteryApi.duplicateLottery(id as string),
    handleResponse: (res: ILotteryItem) => {
      if (res) {
        setLotteryDetail(res)
      }
      return res
    },
    handleError: () => {
      navigate(routePaths.lottery.list)
    },
    dependencies: []
  })

  const customizeRequiredMark = (label: React.ReactNode, { required }: { required: boolean }) => (
    <>
      {required && <span className='text-text-danger font-bold'>＊</span>}
      {label}
    </>
  )

  const setFormValues = (detail: ILotteryItem) => {
    form.setFieldsValue({
      lottery_name: detail.lottery_name,
      preview_flag: detail.preview_flag === '1' ? true : false,
      pause_flag: detail.pause_flag === '1' ? true : false,
      num_of_winners: detail.num_of_winners,
      campaign_company_id: detail.summary.campaign_company_id,
      prize_name: detail.summary.prize_name,
      target_screenshot_explanation: detail.summary.target_screenshot_explanation,
      application_times: detail.summary.application_times,
      notes: detail.summary.notes || '',
      top_text: detail.summary.top_text || '',
      start_date: dayjs(detail.summary.start_at),
      start_time: dayjs(detail.summary.start_at),
      end_date: dayjs(detail.summary.ended_at),
      end_time: dayjs(detail.summary.ended_at),
      prize_img: detail.summary.prize_img_link
        ? {
            uid: '-1',
            name: 'prize_image.jpg',
            status: 'done',
            url: detail.summary.prize_img_link
          }
        : undefined,
      same_screenshot_img: detail.summary.same_screenshot_img_link
        ? {
            uid: '-2',
            name: 'same_screenshot_image.jpg',
            status: 'done',
            url: detail.summary.same_screenshot_img_link
          }
        : undefined
    })
    setPreviewFlag(detail.preview_flag === '1')
    setPauseFlag(detail.pause_flag === '1')
  }

  useEffect(() => {
    if (lotteryDetail) {
      setFormValues(lotteryDetail)
    }
  }, [lotteryDetail])

  const handlePreviewFlagChange = (checked: boolean) => {
    setPreviewFlag(checked)
    form.setFieldValue('preview_flag', checked)
  }

  const handlePauseFlagChange = (checked: boolean) => {
    setPauseFlag(checked)
    form.setFieldValue('pause_flag', checked)
  }

  return (
    <PrivateLayout
      breadcrumb={{
        items: [{ title: '抽選一覧', href: routePaths.lottery.list }, { title: '抽選複製' }],
        className: 'pt-4 pb-8 !mb-0 bg-layout-background sticky top-0 z-10'
      }}
      mainClassName='pt-0'
      defaultOpenKeys={[CATEGORY_MENUS_ITEMS[3].key]}
      loading={isFetchingCompanies || isFetchingLotteryDetail}
    >
      <div className='pb-4 sticky top-[70px] bg-layout-background z-10'>
        <h1 className='font-bold text-[24px] leading-[38px] mb-12'>抽選複製</h1>
        <p>
          <span className='text-text-danger font-bold'>＊</span>は必須項目です。
        </p>
      </div>

      <div className='w-[630px] max-w-[calc(100%-400px)]'>
        <Form
          form={form}
          layout='vertical'
          onFinish={handleSubmit}
          requiredMark={customizeRequiredMark}
          size='large'
        >
          {/* Basic Information */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              基本情報
            </div>

            <Form.Item
              name='campaign_company_id'
              label={LABELS.company_id}
              rules={[{ required: true, message: fieldRequiredMessage(LABELS.company_id) }]}
            >
              <Select
                options={[
                  ...companiesList.map((company) => ({
                    label: company.company_id,
                    value: company.company_id
                  }))
                ]}
              />
            </Form.Item>

            <Form.Item
              name='lottery_name'
              label={LABELS.lottery_name}
              rules={[{ required: true, message: fieldRequiredMessage(LABELS.lottery_name) }]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name='num_of_winners'
              label={LABELS.num_of_winners}
              rules={[{ required: true, message: fieldRequiredMessage(LABELS.num_of_winners) }]}
            >
              <Input />
            </Form.Item>
          </div>

          {/* Prize Information */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              賞品情報
            </div>

            <Form.Item
              name='prize_name'
              label={LABELS.prize_name}
              rules={[{ required: true, message: fieldRequiredMessage(LABELS.prize_name) }]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name='prize_img'
              label={LABELS.prize_img}
              rules={[{ required: true, message: fieldRequiredMessage(LABELS.prize_img) }]}
            >
              <SingleUpload
                name='prize_img'
                data={prizeImageFileList}
                onChange={prizeImageOnChange}
                beforeUpload={beforeUpload}
                accept='image/*'
              />
            </Form.Item>
          </div>

          {/* Application Period */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              応募期間
            </div>

            <div className='flex gap-4 mb-4'>
              <Form.Item
                name='start_date'
                label={LABELS.start_at}
                rules={[{ required: true, message: fieldRequiredMessage(LABELS.start_at) }]}
                className='flex-1'
              >
                <DatePicker className='w-full' format='YYYY/MM/DD' />
              </Form.Item>

              <Form.Item
                name='start_time'
                label='開始時間'
                rules={[{ required: true, message: fieldRequiredMessage('開始時間') }]}
                className='flex-1'
              >
                <TimePicker className='w-full' format='HH:mm' />
              </Form.Item>
            </div>

            <div className='flex gap-4 mb-4'>
              <Form.Item
                name='end_date'
                label={LABELS.end_at}
                rules={[{ required: true, message: fieldRequiredMessage(LABELS.end_at) }]}
                className='flex-1'
              >
                <DatePicker className='w-full' format='YYYY/MM/DD' />
              </Form.Item>

              <Form.Item
                name='end_time'
                label='終了時間'
                rules={[{ required: true, message: fieldRequiredMessage('終了時間') }]}
                className='flex-1'
              >
                <TimePicker className='w-full' format='HH:mm' />
              </Form.Item>
            </div>
          </div>

          {/* Application Settings */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              応募設定
            </div>

            <Form.Item
              name='application_times'
              label={LABELS.application_times}
              rules={[{ required: true, message: fieldRequiredMessage(LABELS.application_times) }]}
            >
              <Radio.Group>
                <Radio value='once_a_day'>1日1回</Radio>
                <Radio value='unlimited'>無制限</Radio>
              </Radio.Group>
            </Form.Item>
          </div>

          {/* Display Settings */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              表示設定
            </div>

            <div className='flex items-center gap-4 mb-4'>
              <span className='text-base font-medium'>プレビューフラグ:</span>
              <Switch checked={previewFlag} onChange={handlePreviewFlagChange} />
            </div>

            <div className='flex items-center gap-4 mb-4'>
              <span className='text-base font-medium'>一時停止フラグ:</span>
              <Switch checked={pauseFlag} onChange={handlePauseFlagChange} />
            </div>

            <Form.Item name='top_text' label={LABELS.top_text}>
              <TextArea rows={4} />
            </Form.Item>
          </div>

          {/* Screenshot Settings */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              スクリーンショット設定
            </div>

            <Form.Item
              name='same_screenshot_img'
              label={LABELS.same_screenshot_img}
              rules={[
                { required: true, message: fieldRequiredMessage(LABELS.same_screenshot_img) }
              ]}
            >
              <SingleUpload
                name='same_screenshot_img'
                data={sameScreenShotImageFileList}
                onChange={sameScreenShotImageOnChange}
                beforeUpload={beforeUpload}
                accept='image/*'
              />
            </Form.Item>

            <Form.Item
              name='target_screenshot_explanation'
              label={LABELS.target_screenshot_explanation}
              rules={[
                {
                  required: true,
                  message: fieldRequiredMessage(LABELS.target_screenshot_explanation)
                }
              ]}
            >
              <TextArea rows={4} />
            </Form.Item>

            <Form.Item name='notes' label={LABELS.notes}>
              <TextArea rows={4} />
            </Form.Item>
          </div>

          {/* Submit Button */}
          <Button
            size='large'
            type='primary'
            htmlType='button'
            onClick={() => form.submit()}
            loading={isLoadingSubmitForm}
            disabled={isLoadingSubmitForm}
            className='w-[156px]'
          >
            作成{' '}
          </Button>
        </Form>
      </div>

      {/* Preview Section */}
      <div className='fixed top-[70px] right-0 w-[400px] h-[calc(100vh-70px)] bg-white border-l border-gray-200 overflow-y-auto'>
        <div className='p-4'>
          <div className='flex items-center justify-between mb-4'>
            <h2 className='text-lg font-bold'>プレビュー</h2>
            <Button onClick={handlePreviewForm} type='primary' size='small'>
              更新
            </Button>
          </div>

          <div className='mb-4'>
            <Radio.Group
              value={previewScreen}
              onChange={(e) => setPreviewScreen(e.target.value)}
              options={PREVIEW_OPTIONS}
              optionType='button'
              buttonStyle='solid'
              size='small'
            />
          </div>

          <div
            className={classNames(
              'border border-gray-300 rounded-lg overflow-hidden',
              'min-h-[500px] bg-gray-50'
            )}
          >
            {previewScreen === LOTTERY_TOP ? (
              <PreviewLotteryTop
                prizeImg={prizeImageFile || form.getFieldValue('prize_img')?.url || null}
                sameScreenshotLink={
                  sameScreenShotImageFile || form.getFieldValue('same_screenshot_img')?.url || null
                }
                topText={previewFormValues?.top_text || ''}
                numOfWinners={previewFormValues?.num_of_winners || ''}
                prizeName={previewFormValues?.prize_name || ''}
                applicationTimes={previewFormValues?.application_times || ''}
                targetScreenshotExplanation={previewFormValues?.target_screenshot_explanation || ''}
                startLottery={
                  previewFormValues?.start_date && previewFormValues?.start_time
                    ? previewFormValues.start_date.toDate().toISOString()
                    : ''
                }
                endLottery={
                  previewFormValues?.end_date && previewFormValues?.end_time
                    ? previewFormValues.end_date.toDate().toISOString()
                    : ''
                }
              />
            ) : (
              <PreviewLotteryAbout
                prizeImg={prizeImageFile || form.getFieldValue('prize_img')?.url || null}
                sameScreenshotLink={
                  sameScreenShotImageFile || form.getFieldValue('same_screenshot_img')?.url || null
                }
                topText={previewFormValues?.top_text || ''}
                numOfWinners={previewFormValues?.num_of_winners || ''}
                prizeName={previewFormValues?.prize_name || ''}
                notes={previewFormValues?.notes || ''}
                applicationTimes={previewFormValues?.application_times || ''}
                targetScreenshotExplanation={previewFormValues?.target_screenshot_explanation || ''}
                startLottery={
                  previewFormValues?.start_date && previewFormValues?.start_time
                    ? previewFormValues.start_date.toDate().toISOString()
                    : ''
                }
                endLottery={
                  previewFormValues?.end_date && previewFormValues?.end_time
                    ? previewFormValues.end_date.toDate().toISOString()
                    : ''
                }
              />
            )}
          </div>
        </div>
      </div>

      {/* Error Modal */}
      <Modal
        open={isSubmitError}
        onCancel={handleCloseErrorModal}
        footer={null}
        closeIcon={<CloseOutlined />}
        centered
        width={500}
      >
        <div className='text-center py-8'>
          <WarningFilled className='text-4xl text-text-danger mb-4' />
          <h3 className='text-lg font-bold mb-2'>{errorMessage.title}</h3>
          <p className='text-base text-gray-600 mb-6'>{errorMessage.message}</p>
          <Button type='primary' onClick={handleCloseErrorModal}>
            閉じる
          </Button>
        </div>
      </Modal>
    </PrivateLayout>
  )
}

export default LotteryDuplicate
