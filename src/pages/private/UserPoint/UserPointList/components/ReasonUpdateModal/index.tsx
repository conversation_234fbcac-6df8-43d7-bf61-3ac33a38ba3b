import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Button, List, Input } from 'antd'
import { IGetPointHistoryResponse, IPointHistory } from '@/types/point-history'
import { formatPointOperation, PLACEHOLDERS } from '../../../constant'
import pointHistoryApi from '@/services/internal/modules/point-history'
import { formatTime } from '@/utils/dateTime'
import { EMPTY_MARK } from '@/constants/common'
import { useNotification } from '@/hooks/useNotification'

const { TextArea } = Input
interface ReasonUpdateModalProps {
  open: boolean
  onClose: () => void
  onUpdate: React.Dispatch<React.SetStateAction<IGetPointHistoryResponse | null>>
  data?: IPointHistory
}

const ReasonUpdateModal: React.FC<ReasonUpdateModalProps> = ({ open, onClose, onUpdate, data }) => {
  const [reason, setReason] = useState('')
  const [loading, setLoading] = useState(false)
  const { showNotification } = useNotification()
  const [resetKey, setResetKey] = useState(0)
  const pointHistory = [
    {
      label: '実行日',
      value: (
        <span>
          {formatTime({
            time: data?.created_at,
            format: 'YYYY/MM/DD HH:mm:ss'
          }) || '-'}
        </span>
      )
    },
    {
      label: 'タイトル',
      value: data?.event_name ? (
        <span className='whitespace-pre-wrap'>{data?.event_name}</span>
      ) : (
        <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>
      )
    },
    {
      label: '企業',
      value: data?.company_id ? (
        <span>{data?.company_id}</span>
      ) : (
        <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>
      )
    },
    {
      label: 'ポイント',
      value:
        data?.point_amount === 0 ? (
          <span>0</span>
        ) : data?.point_amount ? (
          <span>{formatPointOperation(data.endpoint, data.point_amount)}</span>
        ) : (
          <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>
        )
    },
    {
      label: '対象者',
      value: (() => {
        const { peer_conne_id, blob_url } = data?.users || {}
        if (!peer_conne_id && !blob_url) {
          return <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>
        }

        const link = peer_conne_id?.endsWith('.csv')
          ? peer_conne_id
          : blob_url?.endsWith('.csv')
            ? blob_url
            : null

        return link ? (
          <a href={link} target='_blank' className='text-text-link' rel='noopener noreferrer'>
            CSV リンク
          </a>
        ) : (
          <span>{peer_conne_id}</span>
        )
      })()
    }
  ]

  useEffect(() => {
    if (open) {
      setReason(data?.reason || '')
      setResetKey((prevKey) => prevKey + 1)
    }
  }, [data?.id, data?.reason, open])

  const onSubmit = async () => {
    setLoading(true)
    try {
      const result = await pointHistoryApi.updateReasonPointHistory(data?.id as string, {
        reason
      })

      showNotification({ message: '更新が完了しました' })
      onUpdate((prev) => {
        if (!prev) return prev
        const updatedData = prev.data.map((item) => {
          if (item.id === result.id) {
            return { ...item, ...result }
          }
          return item
        })
        return { ...prev, data: updatedData }
      })
    } catch (error) {
      const errorMessage = (error as { response?: { data?: { errors?: { message?: string } } } })
        ?.response?.data?.errors?.message
      showNotification({ message: errorMessage, type: 'error' })
    } finally {
      setLoading(false)
      onClose()
    }
  }

  return (
    <Modal
      open={open}
      onClose={onClose}
      maskClosable={false}
      onCancel={onClose}
      title='理由を更新'
      className='modal-scroll-wrapper'
      footer={[
        <Button
          key='close'
          color='primary'
          variant='outlined'
          size='large'
          className='mt-2'
          onClick={onClose}
        >
          キャンセル
        </Button>,
        <Button
          key='submit'
          size='large'
          className='mt-2'
          type='primary'
          onClick={onSubmit}
          disabled={!reason.trim()}
          loading={loading}
        >
          確定
        </Button>
      ]}
      width={676}
      centered
    >
      <div className='scroll-modal-content'>
        <div>
          <p className='text-[13px] mt-6'>
            <span className='text-text-danger'>＊</span>は必須項目です。
          </p>

          <div className='border border-solid border-border-description rounded-lg mt-4'>
            <List
              itemLayout='horizontal'
              dataSource={pointHistory}
              renderItem={(history) => (
                <List.Item className='!p-0'>
                  <div className='flex w-full'>
                    <span className='w-1/2 bg-layout-background py-[9px] px-4 text-[#000000A6] border-r border-solid border-border-description'>
                      {history.label}
                    </span>
                    <div className='w-1/2 py-[9px] px-4'>{history.value}</div>
                  </div>
                </List.Item>
              )}
            />
          </div>

          <div className='flex flex-col mb-4'>
            <div className='mr-6 mb-3 mt-5'>理由:</div>
            <TextArea
              key={resetKey}
              rows={2}
              value={reason}
              placeholder={PLACEHOLDERS.REASON}
              onChange={(event) => setReason(event.currentTarget.value)}
            />
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default ReasonUpdateModal
