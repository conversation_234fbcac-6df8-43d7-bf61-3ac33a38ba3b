/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-irregular-whitespace */
import React, { useEffect, useState } from 'react'
import { Modal, Button, Form, Alert, Input, Upload } from 'antd'
import { customizeRequiredMark } from '@/utils/form'
import { RcFile } from 'antd/es/upload'
import type { UploadFile } from 'antd/es/upload/interface'
import pointManagementApi from '@/services/internal/modules/point-management'
import { useNotification } from '@/hooks/useNotification'
import FileCsvOutlined from '@/assets/icons/FileCsvOutlined.svg'
import { LABELS, MESSAGES } from '../../../constant'
import { parseApiErrorMessages, parseErrorDetails } from '@/utils/errorHandling'
import Select from '@/components/Select'
import { ICompanyData } from '@/types/image-form'
import SingleUpload from '@/components/SingleUpload'
import { objectToFormData } from '@/utils/convert'
import { BAD_REQUEST, FORBIDDEN } from '@/constants/statusResponse'
import { messages } from '@/constants/message'
import { FILE_SIZE_LIMIT, INVALID_FILE_FORMAT_CSV, INVALID_FILE_SIZE } from '@/constants/common'
import {
  CONVERTED_ERROR_MESSAGES_RESPONSE,
  ERROR_MESSAGES_RESPONSE
} from '@/pages/private/Audience/constant'
const { TextArea } = Input

interface PointSubtractModalProps {
  companiesList?: ICompanyData[]
  open: boolean
  onClose: () => void
}

interface IFormValues {
  point_users_file: { file: File; fileList: UploadFile<RcFile>[] } | null
  point: number
  event_name?: string
  company_id?: string
  reason?: string
}

const PointSubtractModal: React.FC<PointSubtractModalProps> = ({
  companiesList,
  open,
  onClose
}) => {
  const [form] = Form.useForm<IFormValues>()
  const [csvFileList, setCsvFileList] = useState<UploadFile[]>([])
  const [loading, setLoading] = useState(false)
  const [errorList, setErrorList] = useState<string[]>([])
  const [showErrorAlert, setShowErrorAlert] = useState<boolean>(true)
  const { showNotification } = useNotification()
  const beforeUpload = (file: RcFile) => {
    const isCsv = file.type === 'text/csv' || file.name.endsWith('.csv')
    const isFileTooLarge = file.size > FILE_SIZE_LIMIT

    if (!isCsv) {
      showNotification({
        message: INVALID_FILE_FORMAT_CSV,
        type: 'error'
      })
      return Upload.LIST_IGNORE
    }

    if (isFileTooLarge) {
      showNotification({
        message: INVALID_FILE_SIZE,
        type: 'error'
      })
      return Upload.LIST_IGNORE
    }

    return false
  }
  const handleAlertClose = () => setShowErrorAlert(false)

  const handleErrorResponse = (errors: unknown) => {
    const { errorStatus, errorCode, errorMessage } = parseErrorDetails(errors)

    if (errorStatus === FORBIDDEN) {
      setErrorList([messages.error.forbiddenAction])
      return
    }

    if (errorStatus === BAD_REQUEST && errorCode === 'ERR_BAD_REQUEST' && !errorMessage) {
      // Handle CSV file validation errors
      const csvErrors = (errors as any)?.response?.data?.errors?.file_csv
      if (csvErrors && Array.isArray(csvErrors)) {
        console.log(csvErrors)
        const message = `${csvErrors.length}件のpeer_conne_idが正しくありません。`
        setErrorList([message])
      }
      return
    }

    if (errorMessage) {
      setErrorList([
        errorMessage === ERROR_MESSAGES_RESPONSE.invalidColumnName
          ? CONVERTED_ERROR_MESSAGES_RESPONSE.invalidColumnName
          : errorMessage
      ])
      return
    }

    // Handle general API errors as fallback
    const errorDetails = parseApiErrorMessages(errors, true)
    setErrorList(errorDetails?.messageArray || [])
  }

  const onSubmit = async (values: IFormValues) => {
    setErrorList([])
    setShowErrorAlert(true)
    setLoading(true)

    try {
      const csvFile = values.point_users_file?.fileList[0].originFileObj
      const formDataObj = {
        company_id: values.company_id,
        event_name: values.event_name,
        point: values.point,
        reason: values.reason
      }
      const formData = objectToFormData(formDataObj)

      if (csvFile) {
        formData.append('point_users_file', csvFile)
      }

      const response = await pointManagementApi.subtractPoint(formData)

      if (response.isSuccess) {
        showNotification({ message: MESSAGES.createSuccess })
        form.resetFields()
        onClose()
      }

      if (!response.isSuccess && response.data.failedResults.length) {
        const failureMessages = response.data.failedResults.map(
          (error: any) => `${error.pcId}: ${error.message}`
        )
        setErrorList(failureMessages)
      }
    } catch (errors) {
      handleErrorResponse(errors)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    form.resetFields()
    setErrorList([])
    setCsvFileList([])
  }, [open])

  return (
    <>
      <Modal
        open={open}
        onClose={onClose}
        onCancel={onClose}
        maskClosable={false}
        title='ポイントをはく奪'
        className='modal-scroll-wrapper'
        footer={[
          <Button
            disabled={loading}
            key='close'
            color='primary'
            variant='outlined'
            size='large'
            className='mt-2'
            onClick={onClose}
          >
            キャンセル
          </Button>,
          <Button
            key='submit'
            size='large'
            className='mt-2'
            type='primary'
            onClick={() => form.submit()}
            loading={loading}
            disabled={loading}
          >
            確定{' '}
          </Button>
        ]}
        width={676}
        centered
      >
        <div className='scroll-modal-content'>
          <div>
            {errorList.length > 0 &&
              showErrorAlert &&
              errorList.map((error, index) => (
                <Alert
                  key={index}
                  message={error}
                  type='error'
                  showIcon
                  className='mb-4 mt-4'
                  closable
                  onClose={handleAlertClose}
                />
              ))}
            <p className='text-[13px] mt-6 mb-4'>
              <span className='text-text-danger'>＊</span>は必須項目です。
            </p>

            <Form
              layout='vertical'
              name='point_users_file'
              requiredMark={customizeRequiredMark}
              onFinish={onSubmit}
              form={form}
              className='space-y-4'
            >
              <Form.Item
                label={LABELS.EVENT_NAME}
                initialValue='Subtract users points'
                name='event_name'
                className='mb-4'
                rules={[
                  {
                    required: true,
                    message: MESSAGES.required
                  }
                ]}
              >
                <Input size='large' />
              </Form.Item>

              <Form.Item
                label={LABELS.COMPANY_ID}
                name='company_id'
                initialValue='C0000'
                rules={[
                  {
                    required: true,
                    message: MESSAGES.required
                  }
                ]}
              >
                <Select
                  size='large'
                  options={[
                    ...(companiesList || []).map((company) => ({
                      label: company.company_id || '',
                      value: company.company_id || ''
                    }))
                  ]}
                />
              </Form.Item>

              <Form.Item
                label={LABELS.POINT}
                name='point'
                rules={[{ required: true, message: MESSAGES.required }]}
              >
                <div>
                  <Input size='large' className='w-[200px]' />
                  <span className='ml-3'>pt</span>
                </div>
              </Form.Item>

              <Form.Item label={LABELS.REASON} name='reason'>
                <TextArea size='large' style={{ height: 50 }} />
              </Form.Item>

              <Form.Item
                label={LABELS.POINT_USERS_FILE}
                required
                name='point_users_file'
                rules={[
                  {
                    required: true,
                    message: MESSAGES.required
                  }
                ]}
              >
                <SingleUpload
                  name='point_users_file'
                  accept='.csv'
                  beforeUpload={beforeUpload}
                  data={csvFileList}
                  onChange={({ fileList }) => setCsvFileList(fileList)}
                  customIcon={<img src={FileCsvOutlined} alt='FileCsvOutlined' />}
                />
              </Form.Item>
            </Form>

            <div className='px-6 py-3 mt-4 bg-[#FCEFE9] text-[13px]'>
              <p>※ CSV形式のファイルをアップロードしてください。（単体アップロードのみ）</p>
              <p>※ ヘッダーに次の項目を含めてください。</p>
              <p>　 peer_conne_id</p>
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default PointSubtractModal
