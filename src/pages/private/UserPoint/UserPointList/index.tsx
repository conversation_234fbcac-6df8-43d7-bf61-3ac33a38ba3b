import { CATEGORY_MENUS_ITEMS } from '@/constants/routesElement'
import { useFetch } from '@/hooks/useFetch'
import PrivateLayout from '@/layouts/PrivateLayout'
import { Button, Table } from 'antd'
import { formatTime } from '@/utils/dateTime'
import { DownOutlined, EditFilled } from '@ant-design/icons'
import { ColumnsType } from 'antd/es/table'
import Select from '@/components/Select'
import { useState } from 'react'
import pointHistoryApi from '@/services/internal/modules/point-history'
import {
  IGetPointHistoryParams,
  IGetPointHistoryResponse,
  IPointHistory
} from '@/types/point-history'
import ReasonUpdateModal from './components/ReasonUpdateModal'
import { formatPointOperation } from '../constant'
import { EMPTY_MARK } from '@/constants/common'
import PointAddModal from './components/PointAddModal'
import PointSubtractModal from './components/PointSubtractModal'
import { useCompaniesList } from '@/hooks/useCompaniesList'

const PointHistoryList = () => {
  const [limit, setLimit] = useState(20)
  const [pointHistory, setPointHistoryList] = useState<IGetPointHistoryResponse | null>(null)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [id, setId] = useState<string | null>(null)
  const [isOpenAddPointModal, setIsOpenAddPointModal] = useState(false)
  const [isOpenSubtractPointModal, setIsOpenSubtractPointModal] = useState(false)

  const { companiesList, isFetching: isFetchingCompanies } = useCompaniesList()

  const handleLoadMore = async () => {
    if (!pointHistory?.continuationToken || !pointHistory?.data || isLoadingMore) return

    const currentData = [...pointHistory.data]

    try {
      setIsLoadingMore(true)

      const result = await pointHistoryApi.getPointHistoryList({
        limit,
        token: pointHistory.continuationToken
      })

      setPointHistoryList({
        ...result,
        data: [...currentData, ...result.data]
      })
    } catch (error) {
      console.error(error)
    } finally {
      setIsLoadingMore(false)
    }
  }

  const { isFetching: isFetchingPointHistoryList } = useFetch<IGetPointHistoryResponse>({
    fetcher: (params): Promise<IGetPointHistoryResponse> => {
      const payload = params as IGetPointHistoryParams
      return pointHistoryApi.getPointHistoryList({
        limit: limit,
        token: payload?.token
      })
    },
    handleResponse: (res: IGetPointHistoryResponse) => {
      setPointHistoryList(res)
      return res
    },
    dependencies: [limit]
  })

  const columns: ColumnsType<IPointHistory> = [
    {
      title: '実行日時',
      key: 'created_at',
      width: '15%',
      render: (_, record) => (
        <span>
          {formatTime({
            time: record?.created_at,
            format: 'YYYY/MM/DD HH:mm:ss'
          })}
        </span>
      )
    },
    {
      title: 'タイトル',
      dataIndex: 'event_name',
      key: 'event_name',
      width: '20%',
      render: (_, record) => {
        if (!record?.event_name) return <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>

        return <span className='whitespace-pre-wrap'>{record.event_name}</span>
      }
    },
    {
      title: '企業',
      dataIndex: 'company_id',
      key: 'company_id',
      width: '15%',
      render: (_, record) => {
        if (!record?.company_id) return <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>

        return <span>{record.company_id}</span>
      }
    },
    {
      title: '対象者',
      dataIndex: 'peer_conne_id',
      key: 'peer_conne_id',
      width: '20%',
      render: (_, record) => {
        const { peer_conne_id, blob_url } = record?.users || {}
        if (!peer_conne_id && !blob_url) {
          return <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>
        }

        const link = peer_conne_id?.endsWith('.csv')
          ? peer_conne_id
          : blob_url?.endsWith('.csv')
            ? blob_url
            : null

        return link ? (
          <a href={link} target='_blank' className='text-text-link' rel='noopener noreferrer'>
            CSV リンク
          </a>
        ) : (
          <span>{peer_conne_id}</span>
        )
      }
    },
    {
      title: 'ポイント',
      dataIndex: 'point_amount',
      key: 'point_amount',
      width: '15%',
      render: (_, record) => {
        if (record?.point_amount === 0) return <span className='px-3'>0</span>

        return record.point_amount ? (
          <span>{formatPointOperation(record.endpoint, record.point_amount)}</span>
        ) : (
          <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>
        )
      }
    },
    {
      title: '理由',
      dataIndex: 'reason',
      key: 'reason',
      width: '25%',
      render: (_, record) =>
        record.reason ? (
          <span className='whitespace-pre-wrap'>{record.reason}</span>
        ) : (
          <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>
        )
    },
    {
      title: '',
      key: 'action',
      width: '10%',
      render: (_, record) => (
        <Button
          type='link'
          icon={<EditFilled />}
          onClick={() => setId(record.id)}
          className='p-0 font-medium'
        >
          理由更新
        </Button>
      )
    }
  ]

  return (
    <PrivateLayout
      breadcrumb={{
        items: [{ title: 'ポイント操作一覧' }]
      }}
      loading={isFetchingPointHistoryList || isLoadingMore || isFetchingCompanies}
      defaultOpenKeys={[CATEGORY_MENUS_ITEMS[1].key]}
    >
      <div className='flex items-center justify-between mb-8'>
        <h1 className='font-bold text-[24px] leading-[38px]'> ポイント操作一覧</h1>
        <div>
          {/* Check space letter */}
          <Button
            type='primary'
            size='large'
            className='py-3 px-8 leading-6 h-[48px]'
            onClick={() => setIsOpenAddPointModal(true)}
          >
            付与{' '}
          </Button>
          <Button
            color='primary'
            variant='outlined'
            size='large'
            className='ml-7 py-3 px-8 leading-6 h-[48px]'
            onClick={() => setIsOpenSubtractPointModal(true)}
          >
            剥奪{''}
          </Button>
        </div>
      </div>

      <div className='flex items-center justify-end mb-4'>
        <div className='mr-6'>表示上限:</div>
        <Select
          width={130}
          size='large'
          placeholder='~10'
          onChange={(value) => setLimit(value)}
          options={[
            { value: 10, label: '10' },
            { value: 20, label: '20' },
            { value: 50, label: '50' },
            { value: 100, label: '100' },
            { value: 200, label: '200' },
            { value: 500, label: '500' },
            { value: 1000, label: '1000' }
          ]}
          defaultValue={20}
        />
      </div>
      <Table
        className='custom-ant-table'
        columns={columns}
        pagination={false}
        dataSource={pointHistory?.data}
        rowKey='id'
        scroll={{ x: 900 }}
        footer={() => (
          <>
            {
              <>
                {pointHistory?.hasMoreResults && (
                  <div className='bg-white text-center py-2 px-4'>
                    <Button
                      disabled={isFetchingPointHistoryList || isLoadingMore}
                      type='link'
                      icon={<DownOutlined />}
                      iconPosition='end'
                      onClick={handleLoadMore}
                      loading={isLoadingMore}
                    >
                      もっと見る
                    </Button>
                  </div>
                )}
              </>
            }
          </>
        )}
      />

      <ReasonUpdateModal
        open={!!id}
        onClose={() => setId(null)}
        onUpdate={setPointHistoryList}
        data={pointHistory?.data?.find((item) => item.id === id)}
      />

      <PointAddModal open={isOpenAddPointModal} onClose={() => setIsOpenAddPointModal(false)} />

      <PointSubtractModal
        companiesList={companiesList}
        open={isOpenSubtractPointModal}
        onClose={() => setIsOpenSubtractPointModal(false)}
      />
    </PrivateLayout>
  )
}

export default PointHistoryList
