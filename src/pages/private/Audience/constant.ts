export const PLACEHOLDERS = {
  AUDIENCE_ID: 'オーディエンスID',
  AUDIENCE_NAME: 'オーディエンス名'
} as const

export const MESSAGES = {
  required: '必須項目が入力されていません。',
  createSuccess: 'オーディエンスの作成が完了しました。',
  editSuccess: '更新が完了しました。',
  nameAlreadyExists: 'すでに同じ名前のオーディエンスが存在します。'
}

export const ERROR_MESSAGES_RESPONSE = {
  invalidFileFormat: 'Validation failed (field pc_id_file expected type is csv)',
  invalidColumnName: 'The header row should be formatted in the following order: peer_conne_id',
  invalidFileData: 'File invalid data',
  invalidFileSize: 'Validation failed (expected size is less than 5242880)'
}

export const CONVERTED_ERROR_MESSAGES_RESPONSE = {
  invalidFileFormat: 'CSV形式のファイルをアップロードしてください。',
  invalidColumnName: 'CSVファイルのヘッダー行に次の列名が必要です：peer_conne_id',
  invalidFileData: 'peer_conne_idが正しくありません。',
  invalidFileSize: '5MBを超えるファイルはアップロードできません。'
}
