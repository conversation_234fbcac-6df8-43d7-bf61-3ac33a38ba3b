/* eslint-disable no-irregular-whitespace */
import { Modal, Form, Input, UploadFile, Alert, Button, Upload } from 'antd'
import { useState } from 'react'
import { MESSAGES, PLACEHOLDERS } from '../../constant'
import audienceApi from '@/services/internal/modules/audience'
import SingleUpload from '@/components/SingleUpload'
import { RcFile } from 'antd/es/upload'
import { customizeRequiredMark } from '@/utils/form'
import { parseErrorDetails, mapErrorMessage } from '@/utils/errorHandling'
import FileCsvOutlined from '@/assets/icons/FileCsvOutlined.svg'
import { useNotification } from '@/hooks/useNotification'
import { FILE_SIZE_LIMIT, INVALID_FILE_FORMAT_CSV, INVALID_FILE_SIZE } from '@/constants/common'

interface ModalCreateAudienceProps {
  isOpen: boolean
  onCancel: () => void
  onSuccess?: () => void
}
interface AudienceFormValues {
  audience_name: string
  csv_file: UploadFile[]
}

const ModalCreateAudience: React.FC<ModalCreateAudienceProps> = ({
  isOpen,
  onCancel,
  onSuccess
}) => {
  const [form] = Form.useForm<AudienceFormValues>()
  const [csvFileList, setCsvFileList] = useState<UploadFile[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errorList, setErrorList] = useState<string[]>([])
  const [showErrorAlert, setShowErrorAlert] = useState<boolean>(true)
  const [step, setStep] = useState<number>(1)
  const [audienceId, setAudienceId] = useState<string>('')
  const [audienceName, setAudienceName] = useState<string>('')

  const { showNotification } = useNotification()
  // Handle step 1 submission - Create audience name
  const handleStep1Submit = async () => {
    try {
      setIsSubmitting(true)
      setErrorList([])

      const values = await form.validateFields(['audience_name'])
      const createResponse = await audienceApi.createAudienceListName(values.audience_name)

      setAudienceId(createResponse.id)
      setAudienceName(createResponse.pc_id_list_name)
      setStep(2)
    } catch (error) {
      const { errorMessage, apiEndpoint } = parseErrorDetails(error)

      if (apiEndpoint === '/pc-ids') {
        setErrorList([MESSAGES.nameAlreadyExists])
      } else {
        const mappedErrorMessage = mapErrorMessage(errorMessage)
        setErrorList(mappedErrorMessage ? [mappedErrorMessage] : [])
      }

      setShowErrorAlert(true)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle step 2 submission - Upload CSV file
  const handleStep2Submit = async () => {
    try {
      setIsSubmitting(true)
      setErrorList([])

      const values = await form.validateFields(['csv_file'])
      const csvFile = values.csv_file[0].originFileObj

      const formData = new FormData()
      if (csvFile) {
        formData.append('pc_id_file', csvFile)
      } else {
        setErrorList([MESSAGES.required])
        setIsSubmitting(false)
        return
      }

      await audienceApi.importAudienceList(audienceId, formData)
      await audienceApi.createAndUpdateAudience(audienceId, { allow_override: '1' })

      showNotification({ message: MESSAGES.createSuccess })

      form.resetFields()
      setCsvFileList([])
      setErrorList([])
      onCancel()
      onSuccess && onSuccess()
    } catch (error) {
      const { errorMessage } = parseErrorDetails(error)
      const mappedErrorMessage = mapErrorMessage(errorMessage)
      setErrorList(mappedErrorMessage ? [mappedErrorMessage] : [])
      setShowErrorAlert(true)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSubmit = () => {
    if (step === 1) {
      handleStep1Submit()
    } else if (step === 2) {
      handleStep2Submit()
    }
  }

  const handleAlertClose = () => {
    setShowErrorAlert(false)
  }

  const beforeUpload = (file: RcFile) => {
    const isCsv = file.type === 'text/csv' || file.name.endsWith('.csv')
    const isFileTooLarge = file.size > FILE_SIZE_LIMIT

    if (!isCsv) {
      showNotification({
        message: INVALID_FILE_FORMAT_CSV,
        type: 'error'
      })
      return Upload.LIST_IGNORE
    }

    if (isFileTooLarge) {
      showNotification({
        message: INVALID_FILE_SIZE,
        type: 'error'
      })
      return Upload.LIST_IGNORE
    }

    return false
  }

  return (
    <Modal
      title='オーディエンス作成'
      open={isOpen}
      maskClosable={false}
      onCancel={onCancel}
      confirmLoading={isSubmitting}
      width={675}
      footer={[
        <Button
          key={1}
          color='primary'
          variant='outlined'
          size='middle'
          className='h-[40px]'
          onClick={onCancel}
        >
          キャンセル
        </Button>,
        <Button
          key={2}
          color='primary'
          type='primary'
          size='middle'
          loading={isSubmitting}
          className='h-[40px] !ml-4'
          onClick={handleSubmit}
        >
          作成{''}
        </Button>
      ]}
    >
      <div className='pt-6 pb-4 max-h-[60vh] overflow-y-auto'>
        {errorList.length > 0 &&
          showErrorAlert &&
          errorList.map((error, index) => (
            <Alert
              key={index}
              message={error}
              type='error'
              showIcon
              className='mb-4'
              closable
              onClose={handleAlertClose}
            />
          ))}
        <p>
          <span className='text-text-danger font-bold'>＊</span>は必須項目です。
        </p>

        <Form
          form={form}
          layout='vertical'
          requiredMark={customizeRequiredMark}
          size='large'
          className='mt-4'
        >
          {step === 1 && (
            <Form.Item
              label='オーディエンス名'
              className='mb-0'
              name='audience_name'
              rules={[{ required: true, message: MESSAGES.required }]}
            >
              <Input placeholder={PLACEHOLDERS.AUDIENCE_NAME} />
            </Form.Item>
          )}
          {step === 2 && (
            <>
              <div className='flex w-full items-center border border-border-description mb-4'>
                <div className='w-1/2 basis-1/2 bg-layout-background py-[9px] px-4 text-[#000000A6] border-r border-solid  self-stretch flex items-center'>
                  オーディエンス名
                </div>
                <div className='w-1/2 basis-1/2 py-[9px] px-4 self-stretch flex items-center'>
                  {audienceName}
                </div>
              </div>
              <Form.Item
                label='ユーザーリストをアップロードしてください'
                name='csv_file'
                rules={[{ required: true, message: MESSAGES.required }]}
                valuePropName='fileList'
                getValueFromEvent={(e) => e && e.fileList}
              >
                <SingleUpload
                  name='csv_file'
                  accept='.csv'
                  beforeUpload={beforeUpload}
                  data={csvFileList}
                  onChange={({ fileList }) => setCsvFileList(fileList)}
                  customIcon={<img src={FileCsvOutlined} alt='FileCsvOutlined' />}
                />
              </Form.Item>
            </>
          )}
        </Form>
        {step === 2 && (
          <div className='px-6 py-3 bg-[#FCEFE9] text-[13px]'>
            <p>※CSV形式のファイルをアップロードしてください。</p>
            <p>※ヘッダーに次の項目を含めてください。</p>
            <p>　peer_conne_id</p>
          </div>
        )}
      </div>
    </Modal>
  )
}

export default ModalCreateAudience
