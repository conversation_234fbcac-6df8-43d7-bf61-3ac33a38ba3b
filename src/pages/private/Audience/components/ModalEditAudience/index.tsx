/* eslint-disable no-irregular-whitespace */
import { Modal, Form, UploadFile, Alert, Button, Descriptions, Upload } from 'antd'
import { useState } from 'react'
import { MESSAGES } from '../../constant'
import audienceApi from '@/services/internal/modules/audience'
import SingleUpload from '@/components/SingleUpload'
import { RcFile } from 'antd/es/upload'
import { customizeRequiredMark } from '@/utils/form'
import { parseErrorDetails, mapErrorMessage } from '@/utils/errorHandling'
import { DescriptionsProps } from 'antd/lib'
import { AudienceEditInfo } from '@/types/audience'
import './style.scss'
import { formatTime } from '@/utils/dateTime'
import FileCsvOutlined from '@/assets/icons/FileCsvOutlined.svg'
import {
  EMPTY_MARK,
  FILE_SIZE_LIMIT,
  INVALID_FILE_FORMAT_CSV,
  INVALID_FILE_SIZE
} from '@/constants/common'
import { useNotification } from '@/hooks/useNotification'

interface ModalEditAudienceProps {
  isOpen: boolean
  onCancel: () => void
  audienceInfo: AudienceEditInfo
  onSuccess?: () => void
}

interface AudienceFormValues {
  csv_file: UploadFile[]
}

const ModalEditAudience: React.FC<ModalEditAudienceProps> = ({
  isOpen,
  onCancel,
  audienceInfo,
  onSuccess
}) => {
  const [form] = Form.useForm<AudienceFormValues>()
  const [csvFileList, setCsvFileList] = useState<UploadFile[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errorList, setErrorList] = useState<string[]>([])
  const [showErrorAlert, setShowErrorAlert] = useState<boolean>(true)

  const { showNotification } = useNotification()

  const handleOk = async () => {
    try {
      setIsSubmitting(true)

      const values = await form.validateFields()
      const csvFile = values.csv_file[0].originFileObj

      const audienceId = audienceInfo.id
      const formData = new FormData()

      if (csvFile) {
        formData.append('pc_id_file', csvFile)
        formData.append('allow_update', '1')
      } else {
        setErrorList([MESSAGES.required])
      }

      await audienceApi.importAudienceList(audienceId, formData)
      await audienceApi.createAndUpdateAudience(audienceId, { allow_override: '1' })

      showNotification({ message: MESSAGES.editSuccess })

      form.resetFields()

      setCsvFileList([])
      setErrorList([])
      onCancel()
      onSuccess && onSuccess()
    } catch (error) {
      const { errorMessage } = parseErrorDetails(error)
      const mappedErrorMessage = mapErrorMessage(errorMessage)
      setErrorList(mappedErrorMessage ? [mappedErrorMessage] : [])
      setShowErrorAlert(true)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleAlertClose = () => {
    setShowErrorAlert(false)
  }

  const beforeUpload = (file: RcFile) => {
    const isCsv = file.type === 'text/csv' || file.name.endsWith('.csv')
    const isFileTooLarge = file.size > FILE_SIZE_LIMIT

    if (!isCsv) {
      showNotification({
        message: INVALID_FILE_FORMAT_CSV,
        type: 'error'
      })
      return Upload.LIST_IGNORE
    }

    if (isFileTooLarge) {
      showNotification({
        message: INVALID_FILE_SIZE,
        type: 'error'
      })
      return Upload.LIST_IGNORE
    }

    return false
  }

  const items: DescriptionsProps['items'] = [
    {
      label: '作成日',
      children: formatTime({
        time: audienceInfo.audience_created_at,
        format: 'YYYY/MM/DD HH:mm:ss'
      })
    },
    {
      label: 'オーディエンスID',
      children: audienceInfo.id
    },
    {
      label: 'オーディエンス名',
      children: audienceInfo.pc_id_list_name
    },
    {
      label: 'ユーザーリスト',
      children: audienceInfo.pc_id_list_blob_url ? (
        <span>ユーザーリストCSV</span>
      ) : (
        <span className='text-[25px]'>{EMPTY_MARK}</span>
      )
    }
  ]

  return (
    <Modal
      title='ユーザーリスト更新'
      open={isOpen}
      maskClosable={false}
      onCancel={onCancel}
      confirmLoading={isSubmitting}
      width={675}
      centered
      footer={[
        <Button
          key={1}
          color='primary'
          variant='outlined'
          size='middle'
          className='h-[40px]'
          onClick={onCancel}
        >
          キャンセル
        </Button>,
        <Button
          key={2}
          color='primary'
          type='primary'
          size='middle'
          loading={isSubmitting}
          className='h-[40px] !ml-4'
          onClick={handleOk}
        >
          更新{''}
        </Button>
      ]}
    >
      <div className='pt-6 pb-4 max-h-[80vh] overflow-y-auto'>
        {errorList.length > 0 &&
          showErrorAlert &&
          errorList.map((error, index) => (
            <Alert
              key={index}
              message={error}
              type='error'
              showIcon
              className='mb-4'
              closable
              onClose={handleAlertClose}
            />
          ))}
        <p>
          <span className='text-text-danger font-bold'>＊</span>は必須項目です。
        </p>

        <div className='mt-4'>
          <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-3'>
            オーディエンス情報
          </div>
          <Descriptions
            column={1}
            bordered
            layout='horizontal'
            items={items}
            className='custom-descriptions-audience'
          />
        </div>

        <Form
          form={form}
          layout='vertical'
          requiredMark={customizeRequiredMark}
          size='large'
          className='mt-5'
        >
          <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-5'>
            新しいユーザーリスト
          </div>
          <Form.Item
            label='ファイルをアップロードしてください。'
            name='csv_file'
            rules={[{ required: true, message: MESSAGES.required }]}
            valuePropName='fileList'
            getValueFromEvent={(e) => e && e.fileList}
          >
            <SingleUpload
              name='csv_file'
              accept='.csv'
              beforeUpload={beforeUpload}
              data={csvFileList}
              onChange={({ fileList }) => setCsvFileList(fileList)}
              customIcon={<img src={FileCsvOutlined} alt='FileCsvOutlined' />}
            />
          </Form.Item>
        </Form>

        <div className='px-6 py-3 bg-[#FCEFE9] text-[13px]'>
          <p>※CSV形式のファイルをアップロードしてください。</p>
          <p>※ヘッダーに次の項目を含めてください。</p>
          <p>　peer_conne_id</p>
        </div>
      </div>
    </Modal>
  )
}

export default ModalEditAudience
