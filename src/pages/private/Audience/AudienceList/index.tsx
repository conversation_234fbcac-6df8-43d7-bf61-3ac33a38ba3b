import { CATEGORY_MENUS_ITEMS } from '@/constants/routesElement'
import { useFetch } from '@/hooks/useFetch'
import PrivateLayout from '@/layouts/PrivateLayout'
import audienceApi from '@/services/internal/modules/audience'
import { Button, Input, DatePicker, Table } from 'antd'
import { PLACEHOLDERS } from '../constant'
import {
  AudienceEditInfo,
  AudienceInfo,
  IGetAudienceListParams,
  IGetAudienceListResponse
} from '@/types/audience'
import { formatTime } from '@/utils/dateTime'
import { DownOutlined, EditFilled } from '@ant-design/icons'
import { ColumnsType } from 'antd/es/table'
import Select from '@/components/Select'
import { useState } from 'react'
import ModalCreateAudience from '../components/ModalCreateAudience'
import ModalEditAudience from '../components/ModalEditAudience'
import { EMPTY_MARK } from '@/constants/common'

const { RangePicker } = DatePicker

const AudienceList = () => {
  const [limit, setLimit] = useState(20)
  const [audienceName, setAudienceName] = useState('')
  const [audienceId, setAudienceId] = useState('')
  const [fromDate, setFromDate] = useState('')
  const [toDate, setToDate] = useState('')
  const [audienceList, setAudienceList] = useState<IGetAudienceListResponse | null>(null)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false)
  const [isOpenModalEdit, setIsOpenModalEdit] = useState(false)
  const [audienceEditInfo, setAudienceEditInfo] = useState<AudienceEditInfo>({} as AudienceEditInfo)

  const handleLoadMore = async () => {
    if (!audienceList?.continuationToken || !audienceList?.data || isLoadingMore) return

    const currentData = [...audienceList.data]

    try {
      setIsLoadingMore(true)

      const result = await audienceApi.getAudienceList({
        limit,
        id: audienceId,
        name: audienceName,
        from: fromDate,
        to: toDate,
        token: audienceList.continuationToken
      })

      setAudienceList({
        ...result,
        data: [...currentData, ...result.data]
      })
    } catch (error) {
      console.error(error)
    } finally {
      setIsLoadingMore(false)
    }
  }

  const { isFetching: isFetchingAudienceList, fetch: fetchAudienceList } =
    useFetch<IGetAudienceListResponse>({
      fetcher: (params): Promise<IGetAudienceListResponse> => {
        const payload = params as IGetAudienceListParams
        return audienceApi.getAudienceList({
          limit: limit,
          id: audienceId,
          name: audienceName,
          from: fromDate,
          to: toDate,
          token: payload?.token
        })
      },
      handleResponse: (res: IGetAudienceListResponse) => {
        setAudienceList(res)
        return res
      },
      dependencies: [limit]
    })

  const columns: ColumnsType<AudienceInfo> = [
    {
      title: '作成日',
      key: 'audience_created_at',
      width: '20%',
      sorter: (a, b) => {
        const aDate = new Date(a.audience.audience_created_at)
        const bDate = new Date(b.audience.audience_created_at)
        return aDate.getTime() - bDate.getTime()
      },
      sortDirections: ['descend', 'ascend'],
      render: (_, record) => (
        <span>
          {formatTime({
            time: record.audience.audience_created_at,
            format: 'YYYY/MM/DD HH:mm:ss'
          })}
        </span>
      )
    },
    {
      title: 'オーディエンスID',
      dataIndex: 'id',
      key: 'id',
      width: '20%'
    },
    {
      title: 'オーディエンス名',
      dataIndex: 'pc_id_list_name',
      key: 'pc_id_list_name',
      width: '25%'
    },
    {
      title: 'ユーザーリスト',
      render: (_, record) => {
        return record.pc_id_list_blob_url ? (
          <span>ユーザーリストCSV</span>
        ) : (
          <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>
        )
      },
      key: 'pc_id_list_blob_url',
      width: '25%'
    },
    {
      title: '',
      key: 'action',
      width: '10%',
      render: (_, record) => (
        <Button
          type='link'
          icon={<EditFilled />}
          onClick={() => {
            setIsOpenModalEdit(true)
            setAudienceEditInfo({
              id: record.id,
              audience_created_at: record.audience.audience_created_at,
              pc_id_list_name: record.pc_id_list_name,
              pc_id_list_blob_url: record.pc_id_list_blob_url || ''
            })
          }}
          className='p-0'
        >
          編集
        </Button>
      )
    }
  ]

  return (
    <PrivateLayout
      breadcrumb={{
        items: [{ title: 'ユーザーリスト・オーディエンス' }]
      }}
      loading={isFetchingAudienceList || isLoadingMore}
      defaultOpenKeys={[CATEGORY_MENUS_ITEMS[1].key]}
    >
      <div className='flex items-center justify-between mb-8'>
        <h1 className='font-bold text-[24px] leading-[38px]'> ユーザーリスト・オーディエンス</h1>
        <Button
          type='primary'
          size='large'
          className='py-3 px-11 leading-6 h-[48px]'
          onClick={() => setIsOpenModalCreate(true)}
        >
          新規作成
        </Button>
      </div>
      <h4 className='mb-4 text-base font-bold'>オーディエンスを検索</h4>
      <div className='mb-8 flex items-center gap-y-2 gap-x-8 flex-wrap'>
        <div className='flex items-center'>
          <span className='text-[14px] mr-3'>オーディエンスID:</span>
          <Input
            placeholder={PLACEHOLDERS.AUDIENCE_ID}
            size='large'
            className='w-[145px] text-sm h-10'
            onChange={(e) => setAudienceId(e.target.value)}
          />
        </div>
        <div className='flex items-center'>
          <span className='text-[14px] mr-3'>オーディエンス名:</span>
          <Input
            placeholder={PLACEHOLDERS.AUDIENCE_NAME}
            size='large'
            className='w-[146px] text-sm h-10'
            onChange={(e) => setAudienceName(e.target.value)}
          />
        </div>
        <div className='flex items-center'>
          <span className='text-[14px] mr-3'>作成日:</span>
          <RangePicker
            allowEmpty
            className='h-10 w-[320px]'
            onChange={(_, dateString) => {
              setFromDate(dateString[0])
              setToDate(dateString[1])
            }}
          />
        </div>
        <Button
          color='primary'
          variant='outlined'
          size='large'
          className='text-sm'
          onClick={() => fetchAudienceList({ limit: limit })}
        >
          検索{''}
        </Button>
      </div>
      <div className='flex items-center justify-end mb-4'>
        <div className='mr-6'>表示上限:</div>
        <Select
          width={130}
          size='large'
          placeholder='~10'
          onChange={(value) => setLimit(value)}
          options={[
            { value: 10, label: '10' },
            { value: 20, label: '20' },
            { value: 50, label: '50' },
            { value: 100, label: '100' },
            { value: 200, label: '200' },
            { value: 500, label: '500' },
            { value: 1000, label: '1000' }
          ]}
          defaultValue={20}
        />
      </div>
      <Table
        className='custom-ant-table'
        columns={columns}
        pagination={false}
        dataSource={audienceList?.data}
        rowKey='id'
        scroll={{ x: 900 }}
        footer={() =>
          audienceList?.hasMoreResults && (
            <div className='bg-white text-center py-2 px-4'>
              <Button
                disabled={isFetchingAudienceList || isLoadingMore}
                type='link'
                icon={<DownOutlined />}
                iconPosition='end'
                onClick={handleLoadMore}
                loading={isLoadingMore}
              >
                もっと見る
              </Button>
            </div>
          )
        }
      />
      {isOpenModalCreate && (
        <ModalCreateAudience
          isOpen={isOpenModalCreate}
          onCancel={() => setIsOpenModalCreate(false)}
          onSuccess={() => fetchAudienceList({ limit })}
        />
      )}

      {isOpenModalEdit && (
        <ModalEditAudience
          isOpen={isOpenModalEdit}
          onCancel={() => setIsOpenModalEdit(false)}
          audienceInfo={audienceEditInfo}
          onSuccess={() => fetchAudienceList({ limit })}
        />
      )}
    </PrivateLayout>
  )
}

export default AudienceList
