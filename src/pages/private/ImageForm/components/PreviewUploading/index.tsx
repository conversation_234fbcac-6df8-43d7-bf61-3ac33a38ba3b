import { RcFile } from 'antd/es/upload'
import { styles } from '../../style'
import { transformText2 } from '@/utils/text'
import { Button } from 'antd'
import Change from '@/assets/icons/Change.svg'
import Trash from '@/assets/icons/Trash.svg'

interface PreviewUploadingProps {
  imgTop?: RcFile | null
  text_preview?: string
  button_text_image_upload_another?: string
  button_text_submit?: string
}

const PreviewUploading: React.FC<PreviewUploadingProps> = ({
  imgTop,
  text_preview,
  button_text_image_upload_another,
  button_text_submit
}) => {
  return (
    <>
      <div className='mb-6'>
        {imgTop && (
          <div className='mb-4'>
            <img
              src={
                typeof imgTop === 'string'
                  ? imgTop
                  : imgTop instanceof File
                    ? URL.createObjectURL(imgTop)
                    : ''
              }
              alt='Top Preview'
              className='h-auto max-h-[200px] w-full  object-contain'
            />
          </div>
        )}

        <pre
          dangerouslySetInnerHTML={{
            __html: transformText2(text_preview || '', styles)
          }}
          style={{ display: 'flex', flexDirection: 'column' }}
          className={styles.base}
        />

        <div className='mt-4'>
          <div className='w-[120px] h-[120px] font-bold text-[11px] bg-[#95F0D1] flex items-center justify-center mx-auto'>
            Uploaded Img
          </div>
          <div className='flex mt-2 gap-5 items-center'>
            <Button
              className='!text-background-main border !border-background-main '
              variant='outlined'
            >
              <img src={Trash} alt='delete icon' width={16} />
              <span className='text-theme-primary ml-1 whitespace-nowrap text-xs font-semibold'>
                画像を削除する
              </span>
            </Button>
            <Button
              className='!text-background-main border !border-background-main '
              variant='outlined'
            >
              <img src={Change} alt='change icon' width={16} />
              <span className='text-theme-primary ml-1 whitespace-nowrap text-xs font-semibold'>
                画像を変更する
              </span>
            </Button>
          </div>
        </div>
      </div>
      <div>
        {button_text_image_upload_another && (
          <Button
            type='primary'
            className='w-full h-10 text-sm rounded shrink-0 btn-external-primary font-bold mb-4'
          >
            {button_text_image_upload_another}
          </Button>
        )}
        {button_text_submit && (
          <Button
            type='primary'
            className='w-full h-10 text-sm rounded shrink-0 btn-external-primary font-bold'
          >
            {button_text_submit}
          </Button>
        )}
      </div>
    </>
  )
}

export default PreviewUploading
