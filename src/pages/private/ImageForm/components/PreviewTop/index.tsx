import { RcFile } from 'antd/es/upload'
import { styles } from '../../style'
import { transformText2 } from '@/utils/text'
import { Button } from 'antd'

interface PreviewTopProps {
  isOutOfPeriod: boolean
  imgTop?: RcFile | null
  imgSample?: RcFile | null
  text_description?: string
  button_text_image_upload?: string
}

const PreviewTop: React.FC<PreviewTopProps> = ({
  isOutOfPeriod,
  imgTop,
  imgSample,
  text_description,
  button_text_image_upload
}) => {
  return (
    <>
      <div className='mb-6'>
        {imgTop && (
          <div className='mb-4'>
            <img
              src={
                typeof imgTop === 'string'
                  ? imgTop
                  : imgTop instanceof File
                    ? URL.createObjectURL(imgTop)
                    : ''
              }
              alt='Top Preview'
              className='h-auto max-h-[200px] w-full  object-contain'
            />
          </div>
        )}

        <pre
          dangerouslySetInnerHTML={{
            __html: transformText2(text_description || '', styles)
          }}
          style={{ display: 'flex', flexDirection: 'column' }}
          className={styles.base}
        />

        {imgSample && (
          <div className='mt-4'>
            <img
              src={
                typeof imgSample === 'string'
                  ? imgSample
                  : imgSample instanceof File
                    ? URL.createObjectURL(imgSample)
                    : ''
              }
              alt='Sample Preview'
              className='w-full h-auto max-h-[115px] object-contain'
            />
          </div>
        )}
      </div>
      {(button_text_image_upload || isOutOfPeriod) && (
        <Button
          type='primary'
          className='w-full h-10 text-sm rounded shrink-0 btn-external-primary font-bold'
          disabled={isOutOfPeriod}
        >
          {isOutOfPeriod ? '現在、ポイント受取申請期間外です' : button_text_image_upload}
        </Button>
      )}
    </>
  )
}

export default PreviewTop
