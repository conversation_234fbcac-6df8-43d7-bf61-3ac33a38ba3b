import { styles } from '../../style'
import { transformText2 } from '@/utils/text'
import { Button } from 'antd'

interface PreviewSubmittedProps {
  text_submitted?: string
}

const PreviewSubmitted: React.FC<PreviewSubmittedProps> = ({ text_submitted }) => {
  return (
    <>
      <div className='mb-6'>
        <pre
          dangerouslySetInnerHTML={{
            __html: transformText2(text_submitted || '', styles)
          }}
          className={styles.base}
          style={{ display: 'flex', flexDirection: 'column' }}
        />

        <div className='space-y-4'>
          <div className='w-full h-[120px] font-bold text-[11px] bg-[#95F0D1] flex items-center justify-center mx-auto'>
            Uploaded Img
          </div>

          <div className='w-[120px] h-[120px] font-bold text-[11px] bg-[#95F0D1] flex items-center justify-center mx-auto'>
            Uploaded Img
          </div>

          <div className='w-full h-[120px] font-bold text-[11px] bg-[#95F0D1] flex items-center justify-center mx-auto'>
            Uploaded Img
          </div>
        </div>
      </div>
      <Button
        type='primary'
        className='w-full h-10 text-sm rounded shrink-0 btn-external-primary font-bold'
      >
        トーク画面に戻る
      </Button>
    </>
  )
}

export default PreviewSubmitted
