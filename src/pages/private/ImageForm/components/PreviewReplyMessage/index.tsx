import Logo from '@/assets/images/logo.png'

interface PreviewReplyMessageProps {
  text_reply_message: string
}

const PreviewReplyMessage: React.FC<PreviewReplyMessageProps> = ({ text_reply_message }) => {
  return (
    <div className='text-[11px]'>
      <div className='user-message flex flex-col items-end mb-[18px]'>
        <div className='w-9 h-9 rounded-full bg-[#D9D9D9] mb-4'></div>
        <div className='flex items-center'>
          <div className='mr-1'>
            <p>read</p>
            <p>11:12</p>
          </div>
          <div className='bg-[#D1FCE0] rounded w-[195px] p-2 relative'>
            受取申請
            <div className='absolute w-0 h-0 border-l-[8px] border-l-transparent border-b-[8px] border-b-[#D1FCE0] border-r-[8px] border-r-transparent right-3 -top-2'></div>
          </div>
        </div>
      </div>
      <div className='reply-message'>
        <div className='w-9 h-9 rounded-full bg-white mb-4 p-1.5'>
          <img src={Logo} alt='peer conne logo' className='w-full h-full' />
        </div>
        <div>
          <div className='bg-white rounded w-[195px] p-2 relative min-h-[30px] break-all whitespace-pre-wrap'>
            {text_reply_message}
            <div className='absolute w-0 h-0 border-l-[8px] border-l-transparent border-b-[8px] border-b-white border-r-[8px] border-r-transparent left-3 -top-2'></div>
          </div>
          <div className='w-[195px] text-end mt-2'>
            <p>12:12</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PreviewReplyMessage
