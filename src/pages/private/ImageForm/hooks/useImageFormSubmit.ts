import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import { RcFile } from 'antd/es/upload'
import { objectToFormData } from '@/utils/convert'
import imageFormApi from '@/services/internal/modules/image-form'
import { routePaths } from '@/constants/routesElement'
import { CREATE_SUCCESS_TOAST, EDIT_SUCCESS_TOAST } from '../constant'
import { IFormValues } from '@/types/image-form'
import { parseApiErrorMessages } from '@/utils/errorHandling'
import { useNotification } from '@/hooks/useNotification'

export const useImageFormSubmit = (
  topImageFile: RcFile | null,
  sampleImageFile: RcFile | null,
  id?: string
) => {
  const [isLoadingSubmitForm, setIsLoadingSubmitForm] = useState(false)
  const [isSubmitError, setIsSubmitError] = useState(false)
  const [isSubmitSuccess, setIsSubmitSuccess] = useState(false)
  const [errorMessage, setErrorMessage] = useState({
    title: '',
    message: ''
  })
  const navigate = useNavigate()
  const { showNotification } = useNotification()

  const handleCloseErrorModal = () => {
    setIsSubmitError(false)
  }

  const handleSubmit = async (values: IFormValues) => {
    setIsLoadingSubmitForm(true)

    try {
      // Format date and time
      const startDateTime = dayjs(values.start_date)
        .hour(values.start_time.hour())
        .minute(values.start_time.minute())
        .second(0)

      const endDateTime = dayjs(values.end_date)
        .hour(values.end_time.hour())
        .minute(values.end_time.minute())
        .second(0)

      const formDataObj = {
        company_id: values.company_id,
        image_count_max: values.image_count_max,
        start_at: startDateTime.toISOString(),
        ended_at: endDateTime.toISOString(),
        is_public: values.is_public,
        is_customer_form: values.is_customer_form,
        cycle: {
          is_monthly: '1',
          monthly_start_date: values.cycle_start || (!values.cycle_end && '1') || '',
          monthly_end_date: values.cycle_end || (!values.cycle_start && '31') || ''
        },
        text: {
          meta_title: values.meta_title,
          text_description: values.text_description || '',
          text_out_of_period: values.text_out_of_period || '',
          text_img_preview: values.text_img_preview || '',
          button_text_image_upload: values.button_text_image_upload || '',
          button_text_image_upload_another: values.button_text_image_upload_another || '',
          button_text_image_upload_max: values.button_text_image_upload_max || '',
          button_text_submit: values.button_text_submit,
          text_submitted: values.text_submitted || '',
          reply_message: {
            type: 'text',
            text: values.reply_message || ''
          }
        }
      }

      const formData = objectToFormData(formDataObj)

      if (topImageFile) {
        formData.append('top_image', topImageFile)
      } else if (id && values.top_image === null) {
        formData.append('top_image', '')
      }

      if (sampleImageFile) {
        formData.append('sample_image', sampleImageFile)
      } else if (id && values.sample_image === null) {
        formData.append('sample_image', '')
      }
      const response = id
        ? await imageFormApi.editImageForm(formData, id)
        : await imageFormApi.createImageForm(formData)

      if (response) {
        setIsSubmitSuccess(true)
        navigate(routePaths.imgForm.list)
        showNotification({ message: id ? EDIT_SUCCESS_TOAST : CREATE_SUCCESS_TOAST })
      }
    } catch (error: unknown) {
      const errorDetails = parseApiErrorMessages(error)
      setErrorMessage(errorDetails)
      setIsSubmitError(true)
    } finally {
      setIsLoadingSubmitForm(false)
    }
  }

  return {
    isLoadingSubmitForm,
    isSubmitSuccess,
    isSubmitError,
    errorMessage,
    handleSubmit,
    handleCloseErrorModal
  }
}
