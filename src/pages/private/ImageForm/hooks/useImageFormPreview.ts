import { useState } from 'react'
import { notification } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { IMG_FORM_TOP } from '../constant'
import { IFormValues } from '@/types/image-form'

export const useImageFormPreview = (form: FormInstance<IFormValues>) => {
  const [previewFormValues, setPreviewFormValues] = useState<IFormValues | null>(null)
  const [previewScreen, setPreviewScreen] = useState(IMG_FORM_TOP)

  const handlePreviewForm = () => {
    try {
      const values = form.getFieldsValue()
      setPreviewFormValues(values)
    } catch (error) {
      notification.error({
        message: 'プレビューの更新中にエラーが発生しました',
        placement: 'topRight'
      })
    }
  }

  return {
    previewFormValues,
    previewScreen,
    setPreviewScreen,
    handlePreviewForm
  }
}
