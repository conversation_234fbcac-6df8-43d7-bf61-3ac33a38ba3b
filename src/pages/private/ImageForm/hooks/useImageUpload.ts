import { useState } from 'react'
import { notification, Upload } from 'antd'
import type { UploadFile, UploadProps } from 'antd/es/upload/interface'
import { RcFile } from 'antd/es/upload'
import { TEXT_ALERT_INVALID_FILE_EXTENSION, IMAGE_UPLOAD_VALIDATE } from '@/constants/img-form'
import { FILE_SIZE_LIMIT, INVALID_FILE_SIZE } from '@/constants/common'

export const useImageUpload = () => {
  const [topImageFile, setTopImageFile] = useState<RcFile | null>(null)
  const [sampleImageFile, setSampleImageFile] = useState<RcFile | null>(null)
  const [topImageFileList, setTopImageFileList] = useState<UploadFile[]>([])
  const [sampleImageFileList, setSampleImageFileList] = useState<UploadFile[]>([])

  const beforeUpload = (file: RcFile) => {
    // Check file extension
    const extension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase()
    const isValidExtension = IMAGE_UPLOAD_VALIDATE.includes(extension)

    if (!isValidExtension) {
      notification.error({
        message: TEXT_ALERT_INVALID_FILE_EXTENSION,
        placement: 'topRight'
      })
      return Upload.LIST_IGNORE
    }

    // Check file size
    const isFileTooLarge = file.size > FILE_SIZE_LIMIT
    if (isFileTooLarge) {
      notification.error({
        message: INVALID_FILE_SIZE,
        placement: 'topRight'
      })
      return Upload.LIST_IGNORE
    }

    return false
  }

  const handleTopImageChange: UploadProps['onChange'] = ({ fileList }) => {
    setTopImageFileList(fileList)
    if (fileList.length > 0 && fileList[0].originFileObj) {
      setTopImageFile(fileList[0].originFileObj)
    } else {
      setTopImageFile(null)
    }
  }

  const handleSampleImageChange: UploadProps['onChange'] = ({ fileList }) => {
    setSampleImageFileList(fileList)
    if (fileList.length > 0 && fileList[0].originFileObj) {
      setSampleImageFile(fileList[0].originFileObj)
    } else {
      setSampleImageFile(null)
    }
  }

  return {
    topImageFile,
    sampleImageFile,
    topImageFileList,
    sampleImageFileList,
    beforeUpload,
    handleTopImageChange,
    handleSampleImageChange
  }
}
