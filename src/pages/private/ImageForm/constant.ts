export const LABELS = {
  COMPANY_ID: '企業ID',
  TITLE: 'タイトル',
  IMAGE_COUNT_MAX: '画像枚数上限',
  SUBMISSION_PERIOD: '応募期間',
  START: '開始',
  END: '終了',
  MONTHLY_REPEAT: '月次の繰り返し',
  IS_PUBLIC: '公開する',
  USER_VERIFICATION: '利用者判定',
  TOP_SCREEN: 'トップ画面',
  TOP_IMAGE: 'トップ画像',
  DESCRIPTION: '本文',
  SAMPLE_IMAGE: 'サンプル画像',
  UPLOAD_BUTTON_LABEL: 'アップロードボタンラベル',
  OUT_OF_PERIOD_SCREEN: '期間外画面',
  IMAGE_PREVIEW_SCREEN: '画像プレビュー画面',
  ADD_IMAGE_BUTTON_LABEL: '画像の追加選択ボタンラベル',
  IMAGE_LIMIT_BUTTON_LABEL: '画像枚数上限のボタンラベル',
  SUBMIT_BUTTON_LABEL: '応募するボタンラベル',
  SUBMITTED_SCREEN: '応募済み画面',
  REPLY_MESSAGE: '応募後メッセージ',
  MESSAGE: 'メッセージ'
} as const

export const PLACEHOLDERS = {
  COMPANY_ID: '入力済企業ID',
  TITLE: '入力済のタイトル',
  IMAGE_COUNT_MAX: '1',
  DATE_SELECT: '日にちを選択',
  DESCRIPTION: '入力済の本文テキスト',
  UPLOAD_BUTTON_LABEL: '入力済のラベルテキスト',
  ADD_IMAGE_BUTTON_LABEL: '入力済のラベルテキスト',
  IMAGE_LIMIT_BUTTON_LABEL: '入力済のラベルテキスト',
  SUBMIT_BUTTON_LABEL: '入力済のラベルテキスト',
  TEXT_SUBMITTED: '入力済の本文テキスト',
  MESSAGE: '入力済の本文テキスト',
  START_DATE: '日付を選択',
  START_TIME: '時間を選択'
} as const

export const VALIDATION_MESSAGES = {
  COMPANY_ID_REQUIRED: '企業IDを選択してください',
  TITLE_REQUIRED: 'タイトルを入力してください',
  IMAGE_COUNT_REQUIRED: '画像枚数上限を選択してください',
  START_DATE_REQUIRED: '開始日を選択してください',
  START_TIME_REQUIRED: '開始時間を選択してください',
  END_DATE_REQUIRED: '終了日を選択してください',
  END_TIME_REQUIRED: '終了時間を選択してください',
  IS_PUBLIC_REQUIRED: '公開/this field is required',
  USER_VERIFICATION_REQUIRED: 'this field is required',
  DESCRIPTION_REQUIRED: '本文を入力してください',
  SUBMIT_BUTTON_REQUIRED: '応募するボタンラベルを入力してください',
  MESSAGE_REQUIRED: 'メッセージを入力してください',
  BUTTON_IMAGE_UPLOAD_REQUIRED: 'アップロードボタンラベルを入力してください'
} as const

export const IMAGE_DELETE_NOTE = '※画像を削除した場合、画面からも削除されます。'

export const IMG_FORM_TOP = 'img_form_top'
export const IMG_FORM_TOP_OUT_OF_PERIOD = 'img_form_top_out_of_period'
export const IMG_FORM_OUT_OF_PERIOD = 'img_form_out_of_period'
export const IMG_FORM_TOP_UPLOADING = 'img_form_top_uploading'
export const IMG_FORM_SUBMITTED = 'img_form_submitted'
export const IMG_FORM_REPLY_MESSAGE = 'img_form_reply_message'

export const PREVIEW_OPTIONS = [
  { value: IMG_FORM_TOP, label: 'トップ画面' },
  { value: IMG_FORM_TOP_OUT_OF_PERIOD, label: 'トップ画面（周期外）' },
  { value: IMG_FORM_OUT_OF_PERIOD, label: '期間外画面' },
  { value: IMG_FORM_TOP_UPLOADING, label: '画像プレビュー画面' },
  { value: IMG_FORM_SUBMITTED, label: '応募済み画面' },
  { value: IMG_FORM_REPLY_MESSAGE, label: '応募後メッセージ' }
]

export const CREATE_SUCCESS_TOAST = '作成が完了しました'
export const EDIT_SUCCESS_TOAST = '更新が完了しました'
export const cycleOptions = Array.from({ length: 31 }, (_, i) => i + 1)
export const imageCountMaxOptions = Array.from({ length: 100 }, (_, i) => i + 1).map((num) => ({
  value: num,
  label: num.toString()
}))
