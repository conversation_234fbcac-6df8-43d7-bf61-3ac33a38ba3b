import { useState } from 'react'
import PrivateLayout from '@/layouts/PrivateLayout'
import { CATEGORY_MENUS_ITEMS, routePaths } from '@/constants/routesElement'
import {
  LABELS,
  PLACEHOLDERS,
  VALIDATION_MESSAGES,
  IMAGE_DELETE_NOTE,
  PREVIEW_OPTIONS,
  IMG_FORM_OUT_OF_PERIOD,
  IMG_FORM_TOP,
  IMG_FORM_TOP_UPLOADING,
  IMG_FORM_SUBMITTED,
  IMG_FORM_TOP_OUT_OF_PERIOD,
  IMG_FORM_REPLY_MESSAGE,
  imageCountMaxOptions,
  cycleOptions
} from '../constant'
import { Form, Input, DatePicker, TimePicker, Radio, Button, Modal } from 'antd'
import { CloseOutlined, LeftOutlined, SwapRightOutlined, WarningFilled } from '@ant-design/icons'
import PreviewOutOfPeriod from '../components/PreviewOutOfPeriod'
import PreviewTop from '../components/PreviewTop'
import PreviewUploading from '../components/PreviewUploading'
import PreviewSubmitted from '../components/PreviewSubmitted'
import PreviewReplyMessage from '../components/PreviewReplyMessage'
import classNames from 'classnames'
import NotationRulesTable from '@/components/NotationRulesModal'
import { useImageUpload, useImageFormPreview, useImageFormSubmit } from '../hooks'
import Select from '@/components/Select'
import SingleUpload from '@/components/SingleUpload'
import { IFormValues } from '@/types/image-form'
import { useCompaniesList } from '@/hooks/useCompaniesList'

const { TextArea } = Input

const ImageFormCreate = () => {
  const [form] = Form.useForm<IFormValues>()
  const [isNotationModalOpen, setIsNotationModalOpen] = useState(false)

  const {
    topImageFile,
    sampleImageFile,
    topImageFileList,
    sampleImageFileList,
    beforeUpload,
    handleTopImageChange,
    handleSampleImageChange
  } = useImageUpload()

  const { previewFormValues, previewScreen, setPreviewScreen, handlePreviewForm } =
    useImageFormPreview(form)

  const { isLoadingSubmitForm, handleSubmit, isSubmitError, errorMessage, handleCloseErrorModal } =
    useImageFormSubmit(topImageFile, sampleImageFile)

  const { companiesList, isFetching: isFetchingCompanies } = useCompaniesList()

  const customizeRequiredMark = (label: React.ReactNode, { required }: { required: boolean }) => (
    <>
      {label}
      {required && <span style={{ color: 'red' }}>*</span>}:
    </>
  )

  return (
    <PrivateLayout
      breadcrumb={{
        items: [
          { title: '画像応募フォーム一覧', href: routePaths.imgForm.list },
          { title: '画像応募フォーム作成' }
        ],
        className: 'pt-4 pb-8 !mb-0 bg-layout-background sticky top-0 z-10'
      }}
      mainClassName='pt-0'
      defaultOpenKeys={[CATEGORY_MENUS_ITEMS[3].key]}
      loading={isFetchingCompanies}
    >
      <div className='pb-4 sticky top-[70px] bg-layout-background z-10'>
        <h1 className='font-bold text-[24px] leading-[38px] mb-12'>画像応募フォーム作成</h1>
        <p className='mb-8'>
          ※独自記法のルールは
          <span
            className='text-text-link-blue cursor-pointer'
            onClick={() => setIsNotationModalOpen(true)}
          >
            [ここ]
          </span>
          を参照
        </p>
        <p>
          <span className='text-text-danger font-bold'>＊</span>は必須項目です。
        </p>
      </div>

      <div className='w-[630px] max-w-[calc(100%-400px)]'>
        <Form
          form={form}
          layout='vertical'
          onFinish={handleSubmit}
          requiredMark={customizeRequiredMark}
          size='large'
        >
          {/* Basic Information */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              共通情報
            </div>

            <Form.Item
              name='company_id'
              label={LABELS.COMPANY_ID}
              rules={[{ required: true, message: VALIDATION_MESSAGES.COMPANY_ID_REQUIRED }]}
            >
              <Select
                options={[
                  ...companiesList.map((company) => ({
                    label: company.company_id,
                    value: company.company_id
                  }))
                ]}
              />
            </Form.Item>

            <Form.Item
              name='meta_title'
              label={LABELS.TITLE}
              rules={[{ required: true, message: VALIDATION_MESSAGES.TITLE_REQUIRED }]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name='image_count_max'
              label={LABELS.IMAGE_COUNT_MAX}
              rules={[{ required: true, message: VALIDATION_MESSAGES.IMAGE_COUNT_REQUIRED }]}
            >
              <Select width={158} options={imageCountMaxOptions} />
            </Form.Item>

            <div>
              <p className='mb-2 custom-label'>
                {LABELS.SUBMISSION_PERIOD}
                <span className='text-sm text-text-danger'>*</span>:
              </p>
              <div className='flex gap-7'>
                <div className='leading-10 w-11'>{LABELS.START}:</div>
                <Form.Item
                  name='start_date'
                  rules={[{ required: true, message: VALIDATION_MESSAGES.START_DATE_REQUIRED }]}
                >
                  <DatePicker className='w-[158px]' placeholder={PLACEHOLDERS.START_DATE} />
                </Form.Item>

                <Form.Item
                  name='start_time'
                  rules={[{ required: true, message: VALIDATION_MESSAGES.START_TIME_REQUIRED }]}
                >
                  <TimePicker
                    className='w-[158px]'
                    format='HH:mm'
                    placeholder={PLACEHOLDERS.START_TIME}
                  />
                </Form.Item>
              </div>
              <div className='flex gap-7'>
                <div className='leading-10 w-11'>{LABELS.END}:</div>
                <Form.Item
                  name='end_date'
                  rules={[{ required: true, message: VALIDATION_MESSAGES.END_DATE_REQUIRED }]}
                >
                  <DatePicker className='w-[158px]' placeholder={PLACEHOLDERS.START_DATE} />
                </Form.Item>

                <Form.Item
                  name='end_time'
                  rules={[{ required: true, message: VALIDATION_MESSAGES.END_TIME_REQUIRED }]}
                >
                  <TimePicker
                    className='w-[158px]'
                    format='HH:mm'
                    placeholder={PLACEHOLDERS.START_TIME}
                  />
                </Form.Item>
              </div>
            </div>
            <div>
              <p className='mb-2'>{LABELS.MONTHLY_REPEAT}:</p>
              <div className='flex gap-4'>
                <Form.Item
                  name='cycle_start'
                  validateTrigger='onChange'
                  rules={[
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        const cycleEnd = getFieldValue('cycle_end')
                        if (!value && cycleEnd) {
                          return Promise.reject('月次の繰り返しは、両方を選択してください')
                        }
                        return Promise.resolve()
                      }
                    })
                  ]}
                >
                  <Select
                    width={158}
                    options={[
                      { label: '-', value: '' },
                      ...cycleOptions.map((day) => ({ label: day, value: day.toString() }))
                    ]}
                    onChange={() => {
                      form.validateFields(['cycle_end'])
                    }}
                    defaultValue={''}
                  />
                </Form.Item>
                <div className='h-10 flex item-center'>
                  <SwapRightOutlined style={{ fontSize: '16px' }} className='text-text-disabled' />
                </div>
                <Form.Item
                  name='cycle_end'
                  validateTrigger='onChange'
                  rules={[
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        const cycleStart = getFieldValue('cycle_start')
                        if (!value && cycleStart) {
                          return Promise.reject('月次の繰り返しは、両方を選択してください')
                        }
                        return Promise.resolve()
                      }
                    })
                  ]}
                >
                  <Select
                    width={158}
                    options={[
                      { label: '-', value: '' },
                      ...cycleOptions.map((day) => ({ label: day, value: day.toString() }))
                    ]}
                    onChange={() => {
                      form.validateFields(['cycle_start'])
                    }}
                    defaultValue={''}
                  />
                </Form.Item>
              </div>
            </div>

            <div className='flex items-center gap-4 mb-4'>
              <p className='custom-label'>
                {LABELS.IS_PUBLIC}
                <span className='text-sm text-text-danger'>*</span>:
              </p>
              <Form.Item
                name='is_public'
                rules={[{ required: true, message: VALIDATION_MESSAGES.IS_PUBLIC_REQUIRED }]}
                initialValue='0'
                className='mb-0'
              >
                <Radio.Group className='flex gap-4 ml-[100px]'>
                  <Radio value='1'>公開</Radio>
                  <Radio value='0'>非公開</Radio>
                </Radio.Group>
              </Form.Item>
            </div>

            <div className='flex items-center gap-4 mb-4'>
              <p className='custom-label'>
                {LABELS.USER_VERIFICATION}
                <span className='text-sm text-text-danger'>*</span>:
              </p>
              <Form.Item
                name='is_customer_form'
                rules={[
                  { required: true, message: VALIDATION_MESSAGES.USER_VERIFICATION_REQUIRED }
                ]}
                initialValue='0'
                className='mb-0'
              >
                <Radio.Group className='flex gap-4 ml-[85px]'>
                  <Radio value='1'>はい</Radio>
                  <Radio value='0'>いいえ</Radio>
                </Radio.Group>
              </Form.Item>
            </div>
          </div>

          {/* Images */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              {LABELS.TOP_SCREEN}
            </div>

            <Form.Item
              name='top_image'
              label={LABELS.TOP_IMAGE}
              valuePropName='fileList'
              getValueFromEvent={(e) => e?.fileList}
            >
              <div>
                <p className='mb-2.5 text-[#6F6F6F] text-xs leading-5'>{IMAGE_DELETE_NOTE}</p>
                <SingleUpload
                  name='top_image'
                  beforeUpload={beforeUpload}
                  data={topImageFileList}
                  onChange={handleTopImageChange}
                />
              </div>
            </Form.Item>

            <Form.Item name='text_description' label={LABELS.DESCRIPTION}>
              <TextArea rows={4} />
            </Form.Item>

            <Form.Item
              name='sample_image'
              label={LABELS.SAMPLE_IMAGE}
              valuePropName='fileList'
              getValueFromEvent={(e) => e?.fileList}
            >
              <div>
                <p className='mb-2.5 text-[#6F6F6F] text-xs leading-5'>{IMAGE_DELETE_NOTE}</p>
                <SingleUpload
                  name='sample_image'
                  beforeUpload={beforeUpload}
                  data={sampleImageFileList}
                  onChange={handleSampleImageChange}
                />
              </div>
            </Form.Item>

            <Form.Item
              name='button_text_image_upload'
              label={LABELS.UPLOAD_BUTTON_LABEL}
              rules={[
                { required: true, message: VALIDATION_MESSAGES.BUTTON_IMAGE_UPLOAD_REQUIRED }
              ]}
            >
              <div>
                <p className='mb-2.5 text-[#6F6F6F] text-xs leading-5'>{IMAGE_DELETE_NOTE}</p>
                <Input />
              </div>
            </Form.Item>
          </div>

          {/* Text out of period */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              {LABELS.OUT_OF_PERIOD_SCREEN}
            </div>

            <Form.Item
              rules={[{ required: true, message: VALIDATION_MESSAGES.DESCRIPTION_REQUIRED }]}
              name='text_out_of_period'
              label={LABELS.DESCRIPTION}
            >
              <TextArea rows={4} />
            </Form.Item>
          </div>

          {/* Text image preview */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              {LABELS.IMAGE_PREVIEW_SCREEN}
            </div>
            <Form.Item name='text_img_preview' label={LABELS.DESCRIPTION}>
              <TextArea rows={4} />
            </Form.Item>

            <Form.Item
              name='button_text_image_upload_another'
              label={LABELS.ADD_IMAGE_BUTTON_LABEL}
            >
              <Input />
            </Form.Item>

            <Form.Item name='button_text_image_upload_max' label={LABELS.IMAGE_LIMIT_BUTTON_LABEL}>
              <Input />
            </Form.Item>

            <Form.Item
              name='button_text_submit'
              label={LABELS.SUBMIT_BUTTON_LABEL}
              rules={[{ required: true, message: VALIDATION_MESSAGES.SUBMIT_BUTTON_REQUIRED }]}
            >
              <Input />
            </Form.Item>
          </div>

          {/* Text submitted */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              {LABELS.SUBMITTED_SCREEN}
            </div>
            <Form.Item name='text_submitted' label={LABELS.DESCRIPTION}>
              <TextArea rows={4} />
            </Form.Item>
          </div>

          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              {LABELS.REPLY_MESSAGE}
            </div>
            <Form.Item
              name='reply_message'
              label={LABELS.MESSAGE}
              rules={[{ required: true, message: VALIDATION_MESSAGES.MESSAGE_REQUIRED }]}
            >
              <TextArea rows={4} />
            </Form.Item>
          </div>

          {/* Submit Button */}
          <Button
            size='large'
            type='primary'
            htmlType='button'
            onClick={() => form.submit()}
            loading={isLoadingSubmitForm}
            disabled={isLoadingSubmitForm}
            className='w-[156px]'
          >
            作成{' '}
          </Button>
        </Form>
      </div>

      {/* Preview img form */}
      <div className='fixed top-[112px] right-8 z-20'>
        <div className='flex items-center gap-6 justify-end'>
          <div>
            <span className='text-sm inline-block mr-4'>プレビュー:</span>
            <Select
              placeholder='画面名'
              size='large'
              defaultValue={IMG_FORM_TOP}
              className='w-[180px]'
              onChange={(value) => setPreviewScreen(value)}
              options={PREVIEW_OPTIONS}
            />
          </div>
          <Button
            color='primary'
            variant='outlined'
            size='large'
            className='text-sm'
            onClick={handlePreviewForm}
          >
            プレビュー更新
          </Button>
        </div>

        <div
          className={classNames(
            'mt-6 w-[310px] bg-white rounded-lg shadow-lg overflow-hidden mr-8 justify-self-end border-2 border-[#BDBDBD]',
            { '!bg-[#B5C8E4]': previewScreen === IMG_FORM_REPLY_MESSAGE }
          )}
        >
          <div className='p-4 border-b border-[#f0f0f0] relative'>
            {previewScreen === IMG_FORM_REPLY_MESSAGE && (
              <LeftOutlined className='text-sm text-black absolute left-2 top-1/2 -translate-y-1/2' />
            )}
            {previewScreen !== IMG_FORM_REPLY_MESSAGE ? (
              <p className='text-sm text-center break-all'>
                {previewFormValues?.meta_title || 'text.meta_title'}
              </p>
            ) : (
              <p className='text-sm text-center break-all'>ピアコネ</p>
            )}
            {previewScreen !== IMG_FORM_REPLY_MESSAGE && (
              <CloseOutlined className='text-sm text-[#999] absolute right-2 top-1/2 -translate-y-1/2' />
            )}
          </div>

          <div className='h-[600px] max-h-[calc(100vh-250px)] overflow-auto p-3 pb-6 flex flex-col justify-between'>
            {(previewScreen === IMG_FORM_TOP || previewScreen === IMG_FORM_TOP_OUT_OF_PERIOD) && (
              <PreviewTop
                isOutOfPeriod={previewScreen === IMG_FORM_TOP_OUT_OF_PERIOD}
                imgTop={topImageFile}
                imgSample={sampleImageFile}
                text_description={previewFormValues?.text_description || ''}
                button_text_image_upload={previewFormValues?.button_text_image_upload || ''}
              />
            )}

            {previewScreen === IMG_FORM_OUT_OF_PERIOD && (
              <PreviewOutOfPeriod textOutOfPeriod={previewFormValues?.text_out_of_period || ''} />
            )}

            {previewScreen === IMG_FORM_TOP_UPLOADING && (
              <PreviewUploading
                imgTop={topImageFile}
                text_preview={previewFormValues?.text_img_preview || ''}
                button_text_image_upload_another={
                  previewFormValues?.button_text_image_upload_another || ''
                }
                button_text_submit={previewFormValues?.button_text_submit || ''}
              />
            )}

            {previewScreen === IMG_FORM_SUBMITTED && (
              <PreviewSubmitted text_submitted={previewFormValues?.text_submitted || ''} />
            )}

            {previewScreen === IMG_FORM_REPLY_MESSAGE && (
              <PreviewReplyMessage
                text_reply_message={previewFormValues?.reply_message || 'text_reply_message'}
              />
            )}

            {previewScreen === IMG_FORM_OUT_OF_PERIOD && (
              <Button
                type='primary'
                className='w-full h-10 text-sm rounded shrink-0 btn-external-primary font-bold'
              >
                トーク画面に戻る
              </Button>
            )}
          </div>
        </div>
      </div>

      <Modal
        open={isNotationModalOpen}
        onClose={() => setIsNotationModalOpen(false)}
        onCancel={() => setIsNotationModalOpen(false)}
        title='独自記法'
        footer={[
          <Button
            size='middle'
            className='h-[40px] mt-3'
            type='primary'
            onClick={() => setIsNotationModalOpen(false)}
          >
            閉じる
          </Button>
        ]}
        width={1140}
        centered
      >
        <NotationRulesTable />
      </Modal>

      <Modal
        open={isSubmitError}
        onCancel={handleCloseErrorModal}
        title={
          <div>
            <WarningFilled className='text-2xl text-text-danger' />
            <span className='text-text-danger ml-2.5'>{errorMessage.title}</span>
          </div>
        }
        footer={[
          <Button
            key={1}
            color='primary'
            variant='outlined'
            size='large'
            className='text-sm'
            onClick={handleCloseErrorModal}
          >
            閉じる
          </Button>
        ]}
        centered
      >
        <p className='text-base whitespace-pre-line'>{errorMessage.message}</p>
      </Modal>
    </PrivateLayout>
  )
}

export default ImageFormCreate
