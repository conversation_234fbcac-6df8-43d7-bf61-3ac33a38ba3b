import Select from '@/components/Select'
import { EMPTY_MARK } from '@/constants/common'
import { CATEGORY_MENUS_ITEMS, routePaths } from '@/constants/routesElement'
import { useFetch } from '@/hooks/useFetch'
import PrivateLayout from '@/layouts/PrivateLayout'
import imageFormApi from '@/services/internal/modules/image-form'
import { IGetImageFormListResponse, IImageFormList, TImageFormSortDate } from '@/types/image-form'
import { formatTime } from '@/utils/dateTime'
import { DownOutlined, EditFilled } from '@ant-design/icons'
import { But<PERSON>, Tooltip } from 'antd'
import Table, { ColumnsType } from 'antd/es/table'
import { useState, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { useCompaniesList } from '@/hooks/useCompaniesList'

const ImageFormList = () => {
  const navigate = useNavigate()

  const [imageFormData, setImageFormData] = useState<IGetImageFormListResponse | null>(null)
  const [selectedCompanyId, setSelectedCompanyId] = useState<string>('')
  const [selectedIsPlublic, setSelectedIsPlublic] = useState<string>('')
  const [displayCount, setDisplayCount] = useState<number>(20)
  const [sortField, setSortField] = useState<string | null>(null)
  const [sortOrder, setSortOrder] = useState<TImageFormSortDate | null>(null)

  const { isFetching: isFetchingImageForm } = useFetch<IGetImageFormListResponse>({
    fetcher: () =>
      imageFormApi.getImageFormList({
        limit: 3000,
        token: imageFormData?.continuationToken
      }),
    handleResponse: (res: IGetImageFormListResponse) => {
      if (!res || !res.data) {
        return res
      }
      // Create a Set to track unique IDs
      const uniqueIds = new Set(imageFormData?.data?.map((item) => item.unique_key) || [])

      // Filter out duplicates from new data
      const newData = res.data.filter((item) => !uniqueIds.has(item.unique_key))

      setImageFormData((prev) => {
        if (!prev) {
          return res
        }

        return {
          ...prev,
          data: [...prev.data, ...newData],
          continuationToken: res.continuationToken,
          hasMoreResults: res.hasMoreResults
        }
      })

      return res
    }
  })

  const { companiesList, isFetching: isFetchingCompanies } = useCompaniesList()

  const filteredData = useMemo(() => {
    const reverseData = [...(imageFormData?.data || [])].sort((a, b) => {
      const aDate = new Date(a.created_at)
      const bDate = new Date(b.created_at)
      return bDate.getTime() - aDate.getTime()
    })
    if ((!selectedCompanyId && !selectedIsPlublic) || !imageFormData?.data) return reverseData

    return reverseData.filter((item) => {
      if (selectedCompanyId && selectedIsPlublic) {
        return item.company_id === selectedCompanyId && item.is_public === selectedIsPlublic
      } else if (selectedCompanyId) {
        return item.company_id === selectedCompanyId
      } else if (selectedIsPlublic) {
        return item.is_public === selectedIsPlublic
      }
    })
  }, [selectedCompanyId, selectedIsPlublic, imageFormData?.data])

  const sortedData = useMemo(() => {
    if (!filteredData || !sortField || !sortOrder) return filteredData

    return [...filteredData].sort((a, b) => {
      const aValue = a[sortField as keyof IImageFormList]
      const bValue = b[sortField as keyof IImageFormList]

      if (['created_at', 'start_at', 'ended_at'].includes(sortField)) {
        const aDate = new Date(aValue as string)
        const bDate = new Date(bValue as string)
        return sortOrder === 'ascend'
          ? aDate.getTime() - bDate.getTime()
          : bDate.getTime() - aDate.getTime()
      }

      return sortOrder === 'ascend'
        ? String(aValue).localeCompare(String(bValue))
        : String(bValue).localeCompare(String(aValue))
    })
  }, [filteredData, sortField, sortOrder])

  const paginatedData = useMemo(() => {
    if (!sortedData) return []
    return sortedData.slice(0, displayCount)
  }, [sortedData, displayCount])

  const isShowloadMore = useMemo(() => {
    return Boolean(filteredData && displayCount < (filteredData?.length || 0))
  }, [imageFormData?.hasMoreResults, filteredData, displayCount])

  const handleLoadMore = () => {
    setDisplayCount((prevCount) => prevCount + 50)
  }

  const columns: ColumnsType<IImageFormList> = [
    {
      title: '作成日',
      key: 'created_at',
      dataIndex: 'created_at',
      width: '10%',
      sorter: true,
      sortDirections: ['descend', 'ascend'],
      render: (_, record) => <span>{formatTime({ time: record.created_at })}</span>
    },
    {
      title: 'ID',
      dataIndex: 'unique_key',
      key: 'unique_key',
      width: '15%'
    },
    {
      title: '企業ID',
      dataIndex: 'company_id',
      key: 'company_id',
      width: '10%'
    },
    {
      title: 'タイトル',
      dataIndex: ['text', 'meta_title'],
      key: 'title',
      width: '15%',
      ellipsis: {
        showTitle: false
      },
      render: (text) => (
        <Tooltip placement='topLeft' title={text}>
          {text}
        </Tooltip>
      )
    },
    {
      title: '期間開始',
      key: 'start_at',
      dataIndex: 'start_at',
      width: '10%',
      sorter: true,
      sortDirections: ['descend', 'ascend'],
      render: (_, record) => <span>{formatTime({ time: record.start_at })}</span>
    },
    {
      title: '期間終了',
      key: 'ended_at',
      dataIndex: 'ended_at',
      width: '10%',
      sorter: true,
      sortDirections: ['descend', 'ascend'],
      render: (_, record) => <span>{formatTime({ time: record.ended_at })}</span>
    },
    {
      title: '周期',
      key: 'cycle',
      width: '10%',
      render: (_, record) =>
        record.cycle && Object.keys(record.cycle).length > 0 ? (
          <>
            {record.cycle.is_monthly === '0' ? (
              <p className='px-3 text-[25px]'>{EMPTY_MARK}</p>
            ) : (
              <p>
                {record.cycle.monthly_start_date} ~ {record.cycle.monthly_end_date}
              </p>
            )}
          </>
        ) : (
          <p className='px-3 text-[25px]'>{EMPTY_MARK}</p>
        )
    },
    {
      title: '公開/非公開',
      key: 'is_public',
      width: '10%',
      render: (_, record) => <span>{record.is_public === '1' ? '公開' : '非公開'}</span>
    },
    {
      title: '',
      key: 'action',
      width: '10%',
      render: (_, record) => (
        <Button
          type='link'
          icon={<EditFilled />}
          onClick={() => navigate(`/img-form/edit/${record.unique_key}`)}
          className='p-0'
        >
          編集
        </Button>
      )
    }
  ]

  return (
    <PrivateLayout
      breadcrumb={{
        items: [{ title: '画像応募フォーム一覧' }]
      }}
      loading={isFetchingImageForm || isFetchingCompanies}
      defaultOpenKeys={[CATEGORY_MENUS_ITEMS[3].key]}
    >
      <div className='flex items-center justify-between mb-8'>
        <h1 className='font-bold text-[24px] leading-[38px]'>画像応募フォーム一覧</h1>
        <Button
          type='primary'
          size='large'
          className='py-3 px-11 leading-6 h-[48px]'
          onClick={() => navigate(routePaths.imgForm.create)}
        >
          新規作成
        </Button>
      </div>
      <div className='mb-4 flex items-center gap-14'>
        <div>
          <span className='text-[14px] mr-5'>企業ID:</span>
          <Select
            size='large'
            placeholder='{company_id}'
            width={180}
            onChange={(value) => setSelectedCompanyId(value)}
            defaultValue={''}
            options={[
              { label: '-', value: '' },
              ...companiesList.map((company) => ({
                label: company.company_id,
                value: company.company_id
              }))
            ]}
          />
        </div>
        <div>
          <span className='text-[14px] mr-5'>公開設定:</span>
          <Select
            size='large'
            placeholder='{is_public}'
            width={130}
            onChange={(value) => setSelectedIsPlublic(value)}
            defaultValue={''}
            options={[
              { label: '-', value: '' },
              { label: '非公開', value: '0' },
              { label: '公開', value: '1' }
            ]}
          />
        </div>
      </div>
      <Table
        className='custom-ant-table'
        columns={columns}
        pagination={false}
        dataSource={paginatedData}
        rowKey='unique_key'
        scroll={{ x: 900 }}
        onChange={(_pagination, _filters, sorter) => {
          const { field, order } = sorter as { field: string; order: TImageFormSortDate }
          setSortField(field)
          setSortOrder(order)
        }}
        footer={() => (
          <>
            {isShowloadMore && (
              <div className='bg-white text-center py-2 px-4'>
                <Button
                  disabled={isFetchingImageForm || isFetchingCompanies}
                  type='link'
                  icon={<DownOutlined />}
                  iconPosition='end'
                  onClick={handleLoadMore}
                >
                  もっと見る
                </Button>
              </div>
            )}
          </>
        )}
      />
    </PrivateLayout>
  )
}

export default ImageFormList
