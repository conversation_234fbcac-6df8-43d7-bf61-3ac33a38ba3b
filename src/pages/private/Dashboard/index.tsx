import { CATEGORY_MENUS_ITEMS } from '@/constants/routesElement'
import PrivateLayout from '@/layouts/PrivateLayout'
import { Button, Input, DatePicker, Table } from 'antd'
import { formatTime } from '@/utils/dateTime'
import { DownOutlined } from '@ant-design/icons'
import { ColumnsType } from 'antd/es/table'
import Select from '@/components/Select'
import { useState } from 'react'
import LogModal from './components/LogModal'
import { IApiHistory, IApiHistoryList } from '@/types/top'
const { RangePicker } = DatePicker

const Dashboard = () => {
  const [, setLimit] = useState(20)
  const [, setAudienceId] = useState('')
  const [, setFromDate] = useState('')
  const [, setToDate] = useState('')
  const [logApiHistory, setlogApiHistory] = useState<
    | {
        type: 'request' | 'response'
        data?: IApiHistory | undefined
      }
    | undefined
  >(undefined)
  const [audienceList] = useState<IApiHistoryList>({
    data: Array.from({ length: 20 }, (_, idx) => ({
      executed_datetime: '2023/10/20 15:30:30',
      admin_login_name: 'SampleID12345',
      api_name: '画像応募フォーム作成',
      status: idx !== 0,
      request: `curl --location 'https://api-uat.peer-conne.jp/api/v1/admin/image-form' \
--header 'Authorization: ••••••' \
--form 'company_id="C0003"' \
--form 'image_count_max="3"' \
--form 'text[meta_title]="ラントリップ応募フォーム"' \
--form 'text[text_out_of_period]="このラントリップ応募フォームは期間外です。

次回の開催期間は[[bold]]12/1～12/10です。[[/bold]]"' \
--form 'text[text_description]="[[center]][[bold]]キャンペーン[[/bold]][[/center]]

画像をアップロードしてキャンペーンに応募できます

[[center]]応募要項[[/center]]
+ 期間
この応募は毎月1日から10日まで応募できます
+ 応募上限
- 期間中は何枚でも応募できます。
- 画像は1回に10枚まで応募できます。

[[center]]注意事項[[/center]]
* 画像は2MGまでです。
* 画像はpng, jpg, jpeg, heif, heicに対応しています。

[[center]]画像サンプル[[/center]]"' \
--form 'text[text_img_preview]="[[center]][[bold]]text_img_preview[[/bold]][[/center]]

応募中の画像がプレビューされます。"' \
--form 'text[button_text_image_upload]="画像をアップロードする"' \
--form 'text[button_text_image_upload_another]="追加で画像をアップロードする"' \
--form 'text[button_text_image_upload_max]="これ以上、画像は応募できません"' \
--form 'text[button_text_submit]="これらの画像で応募する"' \
--form 'text[text_submitted]="[[center]][[bold]]応募ありがとうございました[[/bold]][[/center]]

何度でも応募できます。
またの応募をお待ちしてます。

"' \
--form 'text[reply_message][type]="text"' \
--form 'text[reply_message][text]="reply_message"' \
--form 'top_image=@"postman-cloud:///1ef86b89-8c3f-4be0-a7ca-f2e0ceabeacc"' \
--form 'sample_image=@"postman-cloud:///1ef86b89-8baf-4b30-8b1b-38462bae8bc7"' \
--form 'start_at="2024-11-22T08:45:00+09:00"' \
--form 'ended_at="2030-12-12T00:00:00+09:00"' \
--form 'cycle[is_monthly]="0"' \
--form 'cycle[monthly_start_date]="1"' \
--form 'cycle[monthly_end_date]="31"' \
--form 'is_public="1"' \
--form 'is_customer_form="1"'`,
      response: `{
  "unique_key": "IF1736140178oexz",
  "type": "image-form",
  "pk": "image-form",
  "company_id": "C0003",
  "image_count_max": 3,
  "text": {
      "meta_title": "ラントリップ応募フォーム",
      "text_out_of_period": "このラントリップ応募フォームは期間外です。\n\n次回の開催期間は[[bold]]12/1～12/10です。[[/bold]]",
      "text_description": "[[center]][[bold]]キャンペーン[[/bold]][[/center]]\n\n画像をアップロードしてキャンペーンに応募できます\n\n[[center]]応募要項[[/center]]\n+ 期間\nこの応募は毎月1日から10日まで応募できます\n+ 応募上限\n- 期間中は何枚でも応募できます。\n- 画像は1回に10枚まで応募できます。\n\n[[center]]注意事項[[/center]]\n* 画像は2MGまでです。\n* 画像はpng, jpg, jpeg, heif, heicに対応しています。\n\n[[center]]画像サンプル[[/center]]",
      "text_img_preview": "[[center]][[bold]]text_img_preview[[/bold]][[/center]]\n\n応募中の画像がプレビューされます。",
      "button_text_image_upload": "画像をアップロードする",
      "button_text_image_upload_another": "追加で画像をアップロードする",
      "button_text_image_upload_max": "これ以上、画像は応募できません",
      "button_text_submit": "これらの画像で応募する",
      "text_submitted": "[[center]][[bold]]応募ありがとうございました[[/bold]][[/center]]\n\n何度でも応募できます。\nまたの応募をお待ちしてます。\n\n",
      "reply_message": {
          "type": "text",
          "text": "reply_message"
      }
  },
  "image": {
      "top_image": "https://wellnessstorageuat.blob.core.windows.net/contents/images-form/IF1736140178oexz/06d61c00-3761-4709-bb53-349572fdc229.png",
      "sample_image": "https://wellnessstorageuat.blob.core.windows.net/contents/images-form/IF1736140178oexz/10aaa98c-b965-45ab-b556-50a077b93858.png"
  },
  "start_at": "2024-11-22T08:45:00+09:00",
  "ended_at": "2030-12-12T00:00:00+09:00",
  "cycle": {
      "is_monthly": "0",
      "monthly_start_date": 1,
      "monthly_end_date": 31
  },
  "is_public": "1",
  "is_customer_form": "1",
  "created_at": "2025-01-06T05:09:38.514Z"
}`
    })),
    hasMoreResults: true,
    continuationToken: 'continuationToken'
  })

  const [isLoadingMore] = useState(false)

  const handleLoadMore = async () => {
    //
  }

  const columns: ColumnsType<IApiHistory> = [
    {
      title: '実行日時',
      dataIndex: 'executed_datetime',
      key: 'executed_datetime',
      width: '12%',
      render: (_, record) => (
        <span>
          {formatTime({
            time: record.executed_datetime,
            format: 'YYYY/MM/DD HH:mm:ss'
          })}
        </span>
      )
    },
    {
      title: 'アカウントID',
      dataIndex: 'admin_login_name',
      key: 'admin_login_name',
      width: '10%'
    },
    {
      title: 'API',
      dataIndex: 'api_name',
      key: 'api_name',
      width: '15%'
    },
    {
      title: '実行結果',
      dataIndex: 'status',
      key: 'status',
      width: '8%',
      render: (_, record) => {
        return (
          <div
            className={`flex items-center gap-2 ${record.status ? 'text-[#27AE60]' : 'text-text-danger'}`}
          >
            <p
              className={`w-2 h-2 rounded-full ${record.status ? 'bg-[#27AE60]' : 'bg-text-danger'}`}
            />
            <span>{record.status ? 'Success' : 'Failed'}</span>
          </div>
        )
      }
    },
    {
      title: 'リクエスト',
      dataIndex: 'request',
      key: 'request',
      render: (_, record) => (
        <span
          onClick={() =>
            setlogApiHistory({
              type: 'request',
              data: record
            })
          }
          className='cursor-pointer'
        >
          {record.request}
        </span>
      ),
      ellipsis: true
    },
    {
      title: 'レスポンス',
      dataIndex: 'response',
      key: 'response',
      render: (_, record) => (
        <span
          onClick={() =>
            setlogApiHistory({
              type: 'response',
              data: record
            })
          }
          className='cursor-pointer'
        >
          {record.response}
        </span>
      ),
      ellipsis: true
    }
  ]

  return (
    <PrivateLayout
      breadcrumb={{
        items: [{ title: '管理者サイトTOP' }]
      }}
      loading={isLoadingMore}
      defaultOpenKeys={[CATEGORY_MENUS_ITEMS[0].key]}
    >
      <div className='flex items-center justify-between mb-8'>
        <h1 className='font-bold text-[24px] leading-[38px]'> 管理者API実行ログ</h1>
        <Button type='primary' size='large' className='py-3 px-6 leading-6 h-[48px]'>
          一覧をCSVに出力
        </Button>
      </div>
      <h4 className='mb-4 text-base font-bold'>検索</h4>
      <div className='mb-8 flex items-center gap-y-2 gap-x-8 flex-wrap'>
        <div className='flex items-center'>
          <span className='text-[14px] mr-3'>実行日:</span>
          <RangePicker
            allowEmpty
            className='h-10 w-[320px]'
            onChange={(_, dateString) => {
              setFromDate(dateString[0])
              setToDate(dateString[1])
            }}
          />
        </div>
        <div className='flex items-center'>
          <span className='text-[14px] mr-3'>アカウントID:</span>
          <Input
            placeholder='アカウントID'
            size='large'
            className='w-64 text-sm h-10'
            onChange={(e) => setAudienceId(e.target.value)}
          />
        </div>
        <div className='flex items-center justify-end'>
          <div className='mr-6'>API:</div>
          <Select
            width={130}
            size='large'
            placeholder='~10'
            onChange={(value) => setLimit(value)}
            options={[
              { value: 10, label: '10' },
              { value: 20, label: '20' },
              { value: 50, label: '50' },
              { value: 100, label: '100' },
              { value: 200, label: '200' },
              { value: 500, label: '500' },
              { value: 1000, label: '1000' }
            ]}
            defaultValue={20}
          />
        </div>

        <Button color='primary' variant='outlined' size='large' className='text-sm'>
          検索{''}
        </Button>
      </div>
      <div className='flex items-center justify-end mb-4'>
        <div className='mr-6'>表示件数:</div>
        <Select
          width={130}
          size='large'
          placeholder='~10'
          onChange={(value) => setLimit(value)}
          options={[
            { value: 10, label: '10' },
            { value: 20, label: '20' },
            { value: 50, label: '50' },
            { value: 100, label: '100' },
            { value: 200, label: '200' },
            { value: 500, label: '500' },
            { value: 1000, label: '1000' }
          ]}
          defaultValue={20}
        />
      </div>
      <Table
        className='custom-ant-table'
        columns={columns}
        pagination={false}
        dataSource={audienceList?.data}
        rowKey='id'
        scroll={{ x: 900 }}
        footer={() =>
          audienceList?.hasMoreResults && (
            <div className='bg-white text-center py-2 px-4'>
              <Button
                disabled={isLoadingMore}
                type='link'
                icon={<DownOutlined />}
                iconPosition='end'
                onClick={handleLoadMore}
                loading={isLoadingMore}
              >
                もっと見る
              </Button>
            </div>
          )
        }
      />

      <LogModal
        open={!!logApiHistory}
        onClose={() => setlogApiHistory(undefined)}
        data={logApiHistory}
      />
    </PrivateLayout>
  )
}

export default Dashboard
