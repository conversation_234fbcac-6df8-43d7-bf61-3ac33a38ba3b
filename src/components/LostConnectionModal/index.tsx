import { WarningFilled } from '@ant-design/icons'
import { Modal } from 'antd'
import React from 'react'

interface LostConnectionModalProps {
  isOpen: boolean
}

const LostConnectionModal: React.FC<LostConnectionModalProps> = ({ isOpen }) => {
  return (
    <Modal
      open={isOpen}
      closable={false}
      footer={null}
      zIndex={9999}
      title={
        <div>
          <WarningFilled className='text-2xl text-text-danger' />
          <span className='text-text-danger ml-2.5'>ネットワークエラー</span>
        </div>
      }
      centered
    >
      <p className='text-base whitespace-pre-line'>
        ネットワークに問題が発生しました。ネットワーク環境を確認して、再度アクセスしてください。
      </p>
    </Modal>
  )
}

export default LostConnectionModal
