import { Layout, Menu } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import { useState, useEffect } from 'react'
import './style.scss'
import { sidebarMenuItems } from '@/constants/routesElement'

const { Sider } = Layout

interface SidebarProps {
  defaultOpenKeys?: string[]
}

const Sidebar = ({ defaultOpenKeys = [] }: SidebarProps) => {
  const navigate = useNavigate()
  const location = useLocation()
  const [openKeys, setOpenKeys] = useState<string[]>(defaultOpenKeys)

  useEffect(() => {
    if (defaultOpenKeys.length > 0) {
      setOpenKeys(defaultOpenKeys)
    }
  }, [defaultOpenKeys])

  // Find matching key for current path, including parent routes
  const selectedKey = sidebarMenuItems.reduce((matched, item) => {
    if (item.key === '/') return '/'

    if (item.children) {
      const matchingChild = item.children.find((child) =>
        location.pathname.startsWith(child.key.toString())
      )
      if (matchingChild) {
        return matchingChild.key
      }
    }
    return matched
  }, location.pathname)

  return (
    <Sider
      width={280}
      theme='light'
      className='border-r border-r-[1px_solid_#0000000F overflow-auto'
    >
      <Menu
        mode='inline'
        selectedKeys={[selectedKey]}
        openKeys={openKeys}
        onOpenChange={setOpenKeys}
        items={[...sidebarMenuItems]}
        onClick={({ key }) => navigate(key)}
      />
    </Sider>
  )
}

export default Sidebar
