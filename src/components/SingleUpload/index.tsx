/* eslint-disable no-unused-vars */
import { IMAGE_UPLOAD_ACCEPT } from '@/constants/img-form'
import { Button, Upload } from 'antd'
import type { UploadChangeParam, UploadFile, UploadListType } from 'antd/es/upload/interface'
import { RcFile } from 'antd/es/upload'
import { InboxOutlined, PlusOutlined } from '@ant-design/icons'
interface SingleUploadProps {
  name: string
  listType?: UploadListType
  data: UploadFile[]
  accept?: string
  beforeUpload: (file: RcFile) => string | false
  onChange?: (info: UploadChangeParam<UploadFile<RcFile>>) => void
  customIcon?: React.ReactNode
}

export const SingleUpload = ({
  name,
  listType = 'picture',
  data,
  accept = IMAGE_UPLOAD_ACCEPT,
  onChange,
  beforeUpload,
  customIcon
}: SingleUploadProps) => {
  return (
    <>
      {data.length ? (
        <Upload
          name={name}
          listType={listType}
          maxCount={1}
          fileList={data}
          accept={accept}
          beforeUpload={beforeUpload}
          onChange={(info) => onChange?.(info)}
          iconRender={customIcon ? () => customIcon : undefined}
        />
      ) : (
        <Upload.Dragger
          name={name}
          listType={listType}
          maxCount={1}
          fileList={data}
          accept={accept}
          beforeUpload={beforeUpload}
          onChange={(info) => onChange?.(info)}
          iconRender={customIcon ? () => customIcon : undefined}
        >
          <p className='ant-upload-drag-icon'>
            <InboxOutlined />
          </p>
          <p className='ant-upload-text !text-sm font-bold'>
            ドラッグ&ドロップでファイルを追加する
          </p>
          <p className='ant-upload-hint text-sm mb-4'>または</p>
          <Button icon={<PlusOutlined />} size='middle'>
            ファイルを選択
          </Button>
        </Upload.Dragger>
      )}
    </>
  )
}

export default SingleUpload
