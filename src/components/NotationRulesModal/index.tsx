import { Table } from 'antd'
import { ColumnsType } from 'antd/es/table'
import './style.scss'

const NotationRulesModal = () => {
  const columns: ColumnsType = [
    {
      dataIndex: 'title',
      key: 'title',
      width: '25%',
      render: (text) => <div className='whitespace-pre-wrap'>{text}</div>
    },
    {
      title: '記法 / notation',
      dataIndex: 'notation',
      key: 'notation',
      width: '25%'
    },
    {
      title: '例) 表記 / ex. notation',
      dataIndex: 'example',
      key: 'example',
      width: '25%'
    },
    {
      title: '例）表示 / ex. display',
      dataIndex: 'display',
      key: 'display',
      width: '25%'
    }
  ]

  const data = [
    {
      key: '1',
      title: <div className='text-sm font-semibold'>bulleted list (-)</div>,
      notation: <div className='text-red-400'>-</div>,
      example: '- text',
      display: <div>• text</div>
    },
    {
      key: '2',
      title: <div className='text-sm font-semibold'>bulleted list (*)</div>,
      notation: <div className='text-red-400'>*</div>,
      example: '* text',
      display: (
        <div className='flex items-start'>
          <span className='mr-1'>※</span>
          <span>text</span>
        </div>
      )
    },
    {
      key: '3',
      title: <div className='text-sm font-semibold'>numbered list</div>,
      notation: <div className='text-red-500'>+</div>,
      example: (
        <div>
          + About gift
          <br />+ How to use gift
        </div>
      ),
      display: (
        <div>
          <div>1. About gift</div>
          <div>2. How to use gift</div>
        </div>
      )
    },
    {
      key: '4',
      title: <div className='text-sm font-semibold'>Link text</div>,
      notation: (
        <div className='text-red-500'>
          [[<span className='text-black'>text&gt;URL</span>]]
        </div>
      ),
      example: <div>[[text&gt;https://peer-conne.jp/]]</div>,
      display: (
        <div>
          <span className='text-blue-500 underline'>text </span>
        </div>
      )
    },
    {
      key: '5',
      title: <div className='text-sm font-semibold'>Header</div>,
      notation: (
        <div className='text-red-500'>
          [[bold]]<span className='text-black'>text</span>[[/bold]]
        </div>
      ),
      example: '[[bold]]Bold text[[/bold]]',
      display: <div className='font-bold'>Bold text</div>
    },
    {
      key: '6',
      title: <div className='text-sm font-semibold'>Center align paragraph</div>,
      notation: (
        <div className='text-red-500'>
          [[center]]<span className='text-black'>Header text</span>[[/center]]
        </div>
      ),
      example: (
        <div>
          [[center]][[bold]]Header text[[/bold]]
          <br />
          2nd row[[/center]]
        </div>
      ),
      display: (
        <div className='text-center'>
          <div className='font-bold'>Header text</div>
          <div>2nd row</div>
        </div>
      )
    }
  ]
  return (
    <div className='notation-rules-table mt-6'>
      <Table
        bordered={false}
        columns={columns}
        dataSource={data}
        pagination={false}
        className='custom-table'
        rowHoverable={false}
        size='middle'
      />
    </div>
  )
}

export default NotationRulesModal
