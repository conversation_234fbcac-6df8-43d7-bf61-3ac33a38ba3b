import { LoadingOutlined } from '@ant-design/icons'
import { Spin } from 'antd'

type LoadingProps = {
  className?: string
  positionClassName?: string
}

const Loading: React.FC<LoadingProps> = ({
  className,
  positionClassName = 'absolute top-0 left-0 w-full h-full'
}) => {
  return (
    <div
      className={`z-[99] flex justify-center items-center bg-white opacity-60 ${className} ${positionClassName}`}
    >
      <Spin indicator={<LoadingOutlined style={{ fontSize: 40 }} spin />} />
    </div>
  )
}

export default Loading
