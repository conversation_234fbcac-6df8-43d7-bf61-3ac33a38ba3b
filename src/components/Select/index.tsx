import { Select as AntSelect, SelectProps } from 'antd'
import { DownOutlined } from '@ant-design/icons'

interface CustomSelectProps extends SelectProps {
  width?: number | string
}

const Select: React.FC<CustomSelectProps> = ({ width, ...props }) => {
  return (
    <AntSelect
      suffixIcon={
        <DownOutlined
          style={{
            color: props.disabled ? '#00000040' : 'black'
          }}
        />
      }
      style={{ width }}
      {...props}
    />
  )
}

export default Select
